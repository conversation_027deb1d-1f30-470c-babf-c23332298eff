# **VETLANE ERP PHASE 1 - COMPREHENSIVE TECHNICAL SPECIFICATIONS**

**Project:** Vetlane ERP v4.0  
**Phase:** 1 (Core MVP)  
**Date:** December 2024  
**Author:** Zoe - 100x Engineer  
**Status:** In Development  

## **EXECUTIVE SUMMARY**

This document provides comprehensive technical specifications for all 25 Phase 1 modules of the Vetlane ERP system. Each module is designed to extend Odoo 18 functionality while maintaining best practices, security, and scalability.

### **Phase 1 Module Overview**
- **Total Modules:** 25
- **Core Infrastructure:** 2 modules
- **POS Enhancement:** 5 modules  
- **Inventory & Procurement:** 4 modules
- **Product Management:** 1 module
- **Business Processes:** 3 modules
- **E-commerce Enhancement:** 5 modules
- **Backend & Administrative:** 4 modules
- **Support & Tools:** 1 module

---

## **PART 1: CORE INFRASTRUCTURE MODULES**

### **MODULE 1: `vetlane_base` - Foundation Module**

#### **Module Overview**
**Purpose:** Foundational infrastructure providing shared utilities, security groups, and common functionality for all Vetlane custom modules.

**Dependencies:** `base`, `mail`, `sale`, `purchase`, `stock`, `account`, `point_of_sale`, `website`

#### **Key Models & Fields**

##### **1. Extended Company Model**
```python
class ResCompany(models.Model):
    _inherit = 'res.company'
    
    # Branch Configuration
    branch_type = fields.Selection([
        ('lagos', 'Lagos Branch'),
        ('abuja', 'Abuja Branch')
    ], string='Branch Type', required=True)
    
    whatsapp_number = fields.Char(
        string='Branch WhatsApp Number',
        help='WhatsApp number for this branch'
    )
    
    is_main_branch = fields.Boolean(
        string='Main Branch',
        default=False,
        help='Designates the primary branch for reporting'
    )
    
    pickup_available = fields.Boolean(
        string='Pickup Available',
        default=True,
        help='Allow customers to pick up orders from this branch'
    )
```

##### **2. Audit Trail Model**
```python
class VetlaneAuditLog(models.Model):
    _name = 'vetlane.audit.log'
    _description = 'Audit Trail for Critical Operations'
    _order = 'create_date desc'
    
    model_name = fields.Char('Model', required=True)
    record_id = fields.Integer('Record ID', required=True)
    field_name = fields.Char('Field Name', required=True)
    old_value = fields.Text('Old Value')
    new_value = fields.Text('New Value')
    user_id = fields.Many2one('res.users', 'User', required=True)
    operation = fields.Selection([
        ('create', 'Create'),
        ('write', 'Update'),
        ('unlink', 'Delete')
    ], required=True)
    timestamp = fields.Datetime('Timestamp', default=fields.Datetime.now)
```

##### **3. Base Mixins**
```python
class AuditTrailMixin(models.AbstractModel):
    _name = 'vetlane.audit.mixin'
    _description = 'Mixin for Audit Trail Functionality'
    
    def write(self, vals):
        # Log changes for auditable fields
        for record in self:
            for field, new_value in vals.items():
                if field in record._get_auditable_fields():
                    old_value = getattr(record, field)
                    if old_value != new_value:
                        self.env['vetlane.audit.log'].create({
                            'model_name': record._name,
                            'record_id': record.id,
                            'field_name': field,
                            'old_value': str(old_value),
                            'new_value': str(new_value),
                            'user_id': self.env.user.id,
                            'operation': 'write'
                        })
        return super().write(vals)
    
    def _get_auditable_fields(self):
        """Override in inheriting models to specify auditable fields"""
        return []

class BranchAwareMixin(models.AbstractModel):
    _name = 'vetlane.branch.mixin'
    _description = 'Mixin for Branch-Aware Models'
    
    branch_id = fields.Many2one(
        'res.company',
        string='Branch',
        default=lambda self: self.env.company,
        required=True
    )
```

#### **Security Groups**
```xml
<!-- Security Groups -->
<record id="group_vetlane_cashier" model="res.groups">
    <field name="name">Vetlane Cashier</field>
    <field name="category_id" ref="base.module_category_sales_point_of_sale"/>
</record>

<record id="group_vetlane_supervisor" model="res.groups">
    <field name="name">Vetlane Supervisor</field>
    <field name="category_id" ref="base.module_category_sales_point_of_sale"/>
    <field name="implied_ids" eval="[(4, ref('group_vetlane_cashier'))]"/>
</record>

<record id="group_vetlane_branch_manager" model="res.groups">
    <field name="name">Vetlane Branch Manager</field>
    <field name="category_id" ref="base.module_category_administration"/>
    <field name="implied_ids" eval="[(4, ref('group_vetlane_supervisor'))]"/>
</record>

<record id="group_vetlane_inventory_lead" model="res.groups">
    <field name="name">Vetlane Inventory Lead</field>
    <field name="category_id" ref="base.module_category_warehouse_management"/>
</record>

<record id="group_vetlane_grooming_coordinator" model="res.groups">
    <field name="name">Vetlane Grooming Coordinator</field>
    <field name="category_id" ref="base.module_category_services"/>
</record>

<record id="group_vetlane_delivery_agent" model="res.groups">
    <field name="name">Vetlane Delivery Agent</field>
    <field name="category_id" ref="base.module_category_services"/>
</record>

<record id="group_vetlane_admin" model="res.groups">
    <field name="name">Vetlane Administrator</field>
    <field name="category_id" ref="base.module_category_administration"/>
    <field name="implied_ids" eval="[(4, ref('group_vetlane_branch_manager'))]"/>
</record>
```

#### **Utility Functions**
```python
class VetlaneUtils:
    
    @staticmethod
    def format_nigerian_currency(amount):
        """Format amount as ₦X,XXX.XX"""
        if not amount:
            return "₦0.00"
        return f"₦{amount:,.2f}"
    
    @staticmethod
    def validate_nigerian_phone(phone):
        """Validate Nigerian phone number format"""
        import re
        if not phone:
            return False
        # Remove spaces and special characters
        clean_phone = re.sub(r'[^\d+]', '', phone)
        # Check for valid Nigerian formats
        patterns = [
            r'^\+234[789]\d{9}$',  # +234XXXXXXXXX
            r'^234[789]\d{9}$',    # 234XXXXXXXXX
            r'^0[789]\d{9}$',      # 0XXXXXXXXX
            r'^[789]\d{9}$'        # XXXXXXXXX
        ]
        return any(re.match(pattern, clean_phone) for pattern in patterns)
    
    @staticmethod
    def generate_reference_number(prefix='VET'):
        """Generate unique reference numbers"""
        import random
        import string
        timestamp = fields.Datetime.now().strftime('%Y%m%d%H%M%S')
        random_suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        return f"{prefix}{timestamp}{random_suffix}"
```

#### **Configuration Parameters**
```xml
<data>
    <!-- SMS Gateway Configuration -->
    <record id="config_sms_gateway_url" model="ir.config_parameter">
        <field name="key">vetlane.sms.gateway.url</field>
        <field name="value">https://api.kudisms.net/api/</field>
    </record>
    
    <!-- Expiry Alert Configuration -->
    <record id="config_expiry_alert_days" model="ir.config_parameter">
        <field name="key">vetlane.expiry.alert.days</field>
        <field name="value">30</field>
    </record>
    
    <!-- Nigerian Localization -->
    <record id="config_default_currency" model="ir.config_parameter">
        <field name="key">vetlane.default.currency</field>
        <field name="value">NGN</field>
    </record>
</data>
```

#### **Testing Strategy**
- Unit tests for utility functions
- Security group permission tests
- Audit trail functionality tests
- Nigerian phone validation tests
- Branch-aware mixin tests

#### **Estimated Effort:** Medium (3-4 weeks)
#### **Acceptance Criteria:**
- ✅ All security groups properly configured
- ✅ Audit trail logs all specified field changes
- ✅ Branch-aware mixin works across all models
- ✅ Nigerian phone validation works correctly
- ✅ Common UI templates render properly

---

### **MODULE 2: `vetlane_multi_branch` - Multi-Branch Infrastructure**

#### **Module Overview**
**Purpose:** Implements multi-branch warehouse configuration and location management (FR-INV1)

**Dependencies:** `vetlane_base`, `stock`, `sale`, `purchase`

#### **Key Models & Fields**

##### **1. Extended Stock Warehouse**
```python
class StockWarehouse(models.Model):
    _inherit = 'stock.warehouse'

    branch_id = fields.Many2one(
        'res.company',
        string='Branch',
        required=True,
        default=lambda self: self.env.company
    )

    is_main_warehouse = fields.Boolean(
        string='Main Warehouse',
        default=False,
        help='Designates the primary warehouse for this branch'
    )

    allow_inter_branch_transfer = fields.Boolean(
        string='Allow Inter-Branch Transfers',
        default=True
    )

    @api.model
    def get_branch_warehouses(self, branch_id):
        """Get all warehouses for a specific branch"""
        return self.search([('branch_id', '=', branch_id)])
```

##### **2. Extended Stock Location**
```python
class StockLocation(models.Model):
    _inherit = 'stock.location'

    branch_id = fields.Many2one(
        'res.company',
        string='Branch',
        compute='_compute_branch_id',
        store=True
    )

    location_type = fields.Selection([
        ('main_stock', 'Main Stock'),
        ('quarantine', 'Quarantine'),
        ('damaged', 'Damaged Goods'),
        ('expired', 'Expired Items'),
        ('transit', 'In Transit')
    ], string='Location Type')

    @api.depends('warehouse_id')
    def _compute_branch_id(self):
        for location in self:
            location.branch_id = location.warehouse_id.branch_id.id if location.warehouse_id else False
```

##### **3. Inter-Branch Transfer Model**
```python
class InterBranchTransfer(models.Model):
    _name = 'vetlane.inter.branch.transfer'
    _description = 'Inter-Branch Transfer Request'
    _inherit = ['mail.thread', 'vetlane.audit.mixin']
    _order = 'create_date desc'

    name = fields.Char(
        string='Transfer Reference',
        default=lambda self: VetlaneUtils.generate_reference_number('IBT'),
        required=True
    )

    source_branch_id = fields.Many2one(
        'res.company',
        string='Source Branch',
        required=True,
        default=lambda self: self.env.company
    )

    destination_branch_id = fields.Many2one(
        'res.company',
        string='Destination Branch',
        required=True
    )

    source_warehouse_id = fields.Many2one(
        'stock.warehouse',
        string='Source Warehouse',
        required=True,
        domain="[('branch_id', '=', source_branch_id)]"
    )

    destination_warehouse_id = fields.Many2one(
        'stock.warehouse',
        string='Destination Warehouse',
        required=True,
        domain="[('branch_id', '=', destination_branch_id)]"
    )

    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('in_transit', 'In Transit'),
        ('received', 'Received'),
        ('cancelled', 'Cancelled')
    ], default='draft', tracking=True)

    transfer_line_ids = fields.One2many(
        'vetlane.inter.branch.transfer.line',
        'transfer_id',
        string='Transfer Lines'
    )

    picking_id = fields.Many2one(
        'stock.picking',
        string='Stock Picking',
        readonly=True
    )

    requested_by = fields.Many2one(
        'res.users',
        string='Requested By',
        default=lambda self: self.env.user,
        required=True
    )

    approved_by = fields.Many2one(
        'res.users',
        string='Approved By'
    )

    transfer_date = fields.Datetime(
        string='Transfer Date',
        default=fields.Datetime.now
    )

    notes = fields.Text('Notes')

    def action_confirm(self):
        """Confirm the transfer request"""
        self.state = 'confirmed'
        self._create_stock_picking()

    def action_start_transfer(self):
        """Start the transfer process"""
        self.state = 'in_transit'
        if self.picking_id:
            self.picking_id.action_confirm()

    def action_receive(self):
        """Mark transfer as received"""
        self.state = 'received'
        if self.picking_id:
            self.picking_id.action_done()

    def _create_stock_picking(self):
        """Create stock picking for the transfer"""
        picking_vals = {
            'partner_id': self.destination_branch_id.partner_id.id,
            'location_id': self.source_warehouse_id.lot_stock_id.id,
            'location_dest_id': self.destination_warehouse_id.lot_stock_id.id,
            'picking_type_id': self.source_warehouse_id.out_type_id.id,
            'origin': self.name,
            'move_lines': [(0, 0, {
                'name': line.product_id.name,
                'product_id': line.product_id.id,
                'product_uom_qty': line.quantity,
                'product_uom': line.product_id.uom_id.id,
                'location_id': self.source_warehouse_id.lot_stock_id.id,
                'location_dest_id': self.destination_warehouse_id.lot_stock_id.id,
            }) for line in self.transfer_line_ids]
        }

        picking = self.env['stock.picking'].create(picking_vals)
        self.picking_id = picking.id
        return picking

class InterBranchTransferLine(models.Model):
    _name = 'vetlane.inter.branch.transfer.line'
    _description = 'Inter-Branch Transfer Line'

    transfer_id = fields.Many2one(
        'vetlane.inter.branch.transfer',
        string='Transfer',
        required=True,
        ondelete='cascade'
    )

    product_id = fields.Many2one(
        'product.product',
        string='Product',
        required=True
    )

    quantity = fields.Float(
        string='Quantity',
        required=True,
        default=1.0
    )

    available_qty = fields.Float(
        string='Available Quantity',
        compute='_compute_available_qty'
    )

    reason = fields.Text('Transfer Reason')

    @api.depends('product_id', 'transfer_id.source_warehouse_id')
    def _compute_available_qty(self):
        for line in self:
            if line.product_id and line.transfer_id.source_warehouse_id:
                stock_quant = self.env['stock.quant'].search([
                    ('product_id', '=', line.product_id.id),
                    ('location_id', 'child_of', line.transfer_id.source_warehouse_id.lot_stock_id.id)
                ])
                line.available_qty = sum(stock_quant.mapped('quantity'))
            else:
                line.available_qty = 0.0
```

#### **UI/UX Implementation**
```xml
<!-- Inter-Branch Transfer Form View -->
<record id="view_inter_branch_transfer_form" model="ir.ui.view">
    <field name="name">vetlane.inter.branch.transfer.form</field>
    <field name="model">vetlane.inter.branch.transfer</field>
    <field name="arch" type="xml">
        <form string="Inter-Branch Transfer">
            <header>
                <button name="action_confirm" type="object" string="Confirm"
                        states="draft" class="btn-primary"/>
                <button name="action_start_transfer" type="object" string="Start Transfer"
                        states="confirmed" class="btn-primary"/>
                <button name="action_receive" type="object" string="Mark as Received"
                        states="in_transit" class="btn-success"/>
                <field name="state" widget="statusbar"/>
            </header>
            <sheet>
                <div class="oe_title">
                    <h1><field name="name"/></h1>
                </div>
                <group>
                    <group>
                        <field name="source_branch_id"/>
                        <field name="source_warehouse_id"/>
                        <field name="requested_by"/>
                    </group>
                    <group>
                        <field name="destination_branch_id"/>
                        <field name="destination_warehouse_id"/>
                        <field name="transfer_date"/>
                    </group>
                </group>
                <notebook>
                    <page string="Transfer Lines">
                        <field name="transfer_line_ids">
                            <tree editable="bottom">
                                <field name="product_id"/>
                                <field name="quantity"/>
                                <field name="available_qty" readonly="1"/>
                                <field name="reason"/>
                            </tree>
                        </field>
                    </page>
                    <page string="Notes">
                        <field name="notes"/>
                    </page>
                </notebook>
            </sheet>
            <div class="oe_chatter">
                <field name="message_follower_ids"/>
                <field name="message_ids"/>
            </div>
        </form>
    </field>
</record>
```

#### **Testing Strategy**
- Unit tests for inter-branch transfer creation
- Stock picking creation tests
- Branch warehouse association tests
- Transfer approval workflow tests

#### **Estimated Effort:** Large (4-5 weeks)
#### **Acceptance Criteria:**
- ✅ Warehouses properly associated with branches
- ✅ Inter-branch transfers can be created and processed
- ✅ Stock movements tracked correctly
- ✅ Proper location hierarchy maintained
- ✅ Transfer approval workflow functions properly

---

## **PART 2: POS ENHANCEMENT MODULES**

### **MODULE 3: `vetlane_pos_supervisor_approval` - POS Workflow Controls**

#### **Module Overview**
**Purpose:** Implements supervisor approval workflow, role-based controls, and cashier-only finalization (FR-POS1, FR-POS2, FR-POS4, FR-POS8, FR-POS11)

**Dependencies:** `vetlane_base`, `point_of_sale`

#### **Key Models & Fields**

##### **1. Extended POS Order**
```python
class PosOrder(models.Model):
    _inherit = 'pos.order'
    _inherit = ['pos.order', 'vetlane.audit.mixin']

    requires_supervisor_approval = fields.Boolean(
        string='Requires Supervisor Approval',
        compute='_compute_requires_approval',
        store=True
    )

    supervisor_approved = fields.Boolean(
        string='Supervisor Approved',
        default=False
    )

    supervisor_id = fields.Many2one(
        'res.users',
        string='Approving Supervisor'
    )

    approval_reason = fields.Selection([
        ('discount', 'Discount Applied'),
        ('return', 'Return/Exchange'),
        ('price_override', 'Price Override'),
        ('item_deletion', 'Item Deletion'),
        ('other', 'Other')
    ], string='Approval Reason')

    is_locked = fields.Boolean(
        string='Transaction Locked',
        default=False,
        help='Prevents further modifications after finalization'
    )

    locked_by = fields.Many2one(
        'res.users',
        string='Locked By'
    )

    lock_timestamp = fields.Datetime(
        string='Lock Timestamp'
    )

    @api.depends('lines.discount', 'lines.price_unit')
    def _compute_requires_approval(self):
        """Determine if supervisor approval is required"""
        for order in self:
            requires_approval = False

            # Check for discounts > 10%
            for line in order.lines:
                if line.discount > 10:
                    requires_approval = True
                    break

            # Check for price overrides
            for line in order.lines:
                if line.price_unit != line.product_id.list_price:
                    requires_approval = True
                    break

            order.requires_supervisor_approval = requires_approval

    def action_request_supervisor_approval(self, reason='other'):
        """Request supervisor approval"""
        self.write({
            'approval_reason': reason,
            'requires_supervisor_approval': True
        })

        # Send notification to supervisors
        supervisor_group = self.env.ref('vetlane_base.group_vetlane_supervisor')
        supervisors = self.env['res.users'].search([
            ('groups_id', 'in', supervisor_group.ids)
        ])

        for supervisor in supervisors:
            self.message_post(
                body=f"Supervisor approval requested for order {self.name}. Reason: {reason}",
                partner_ids=[supervisor.partner_id.id]
            )

    def action_supervisor_approve(self):
        """Approve the order (supervisor only)"""
        if not self.env.user.has_group('vetlane_base.group_vetlane_supervisor'):
            raise UserError("Only supervisors can approve orders")

        self.write({
            'supervisor_approved': True,
            'supervisor_id': self.env.user.id
        })

        self.message_post(body=f"Order approved by {self.env.user.name}")

    def action_lock_transaction(self):
        """Lock transaction to prevent further modifications"""
        if not self.env.user.has_group('vetlane_base.group_vetlane_cashier'):
            raise UserError("Only cashiers can finalize transactions")

        self.write({
            'is_locked': True,
            'locked_by': self.env.user.id,
            'lock_timestamp': fields.Datetime.now()
        })

    def _get_auditable_fields(self):
        """Define fields to audit"""
        return ['amount_total', 'supervisor_approved', 'is_locked']
```

##### **2. Extended POS Order Line**
```python
class PosOrderLine(models.Model):
    _inherit = 'pos.order.line'
    _inherit = ['pos.order.line', 'vetlane.audit.mixin']

    original_price = fields.Float(
        string='Original Price',
        help='Original product price before any modifications'
    )

    price_override_reason = fields.Char(
        string='Price Override Reason'
    )

    deleted_by_supervisor = fields.Boolean(
        string='Deleted by Supervisor',
        default=False
    )

    deletion_reason = fields.Char(
        string='Deletion Reason'
    )

    show_cost_price = fields.Boolean(
        string='Show Cost Price',
        compute='_compute_show_cost_price'
    )

    @api.depends('order_id.user_id')
    def _compute_show_cost_price(self):
        """Hide cost price from non-supervisors (FR-POS4)"""
        for line in self:
            user = line.order_id.user_id or self.env.user
            line.show_cost_price = user.has_group('vetlane_base.group_vetlane_supervisor')

    @api.model
    def create(self, vals):
        """Store original price on creation"""
        if 'product_id' in vals and not vals.get('original_price'):
            product = self.env['product.product'].browse(vals['product_id'])
            vals['original_price'] = product.list_price
        return super().create(vals)

    def action_supervisor_delete(self, reason):
        """Delete line item with supervisor approval"""
        if not self.env.user.has_group('vetlane_base.group_vetlane_supervisor'):
            raise UserError("Only supervisors can delete items")

        self.write({
            'deleted_by_supervisor': True,
            'deletion_reason': reason
        })

        # Log the deletion
        self.order_id.message_post(
            body=f"Item {self.product_id.name} deleted by {self.env.user.name}. Reason: {reason}"
        )

        self.unlink()

    def _get_auditable_fields(self):
        """Define fields to audit"""
        return ['price_unit', 'discount', 'qty']
```

#### **JavaScript Extensions**
```javascript
// pos_supervisor_approval.js
odoo.define('vetlane_pos_supervisor_approval.models', function (require) {
    "use strict";

    var models = require('point_of_sale.models');
    var core = require('web.core');
    var _t = core._t;

    // Extend POS Order
    var _super_order = models.Order.prototype;
    models.Order = models.Order.extend({

        initialize: function(attributes, options) {
            _super_order.initialize.call(this, attributes, options);
            this.requires_supervisor_approval = false;
            this.supervisor_approved = false;
            this.is_locked = false;
        },

        add_product: function(product, options) {
            // Check if supervisor approval is required
            var line = _super_order.add_product.call(this, product, options);
            this.check_supervisor_approval_required();
            return line;
        },

        check_supervisor_approval_required: function() {
            var requires_approval = false;

            // Check for discounts > 10%
            this.orderlines.each(function(line) {
                if (line.get_discount() > 10) {
                    requires_approval = true;
                }

                // Check for price overrides
                if (line.get_unit_price() !== line.product.list_price) {
                    requires_approval = true;
                }
            });

            this.requires_supervisor_approval = requires_approval;
            this.trigger('change', this);
        },

        can_finalize: function() {
            // Only cashiers can finalize
            var user = this.pos.get_cashier();
            if (!user.groups_id.includes(this.pos.config.cashier_group_id)) {
                return false;
            }

            // Check if supervisor approval is required and granted
            if (this.requires_supervisor_approval && !this.supervisor_approved) {
                return false;
            }

            return true;
        },

        request_supervisor_approval: function(reason) {
            var self = this;
            return this.pos.rpc({
                model: 'pos.approval.request',
                method: 'create',
                args: [{
                    order_id: this.server_id,
                    request_type: reason,
                    reason: 'Approval required for POS transaction'
                }]
            }).then(function(request_id) {
                self.approval_request_id = request_id;
                // Show notification to user
                self.pos.gui.show_popup('alert', {
                    title: _t('Approval Requested'),
                    body: _t('Supervisor approval has been requested. Please wait for approval.')
                });
            });
        },

        lock_transaction: function() {
            if (this.can_finalize()) {
                this.is_locked = true;
                this.locked_by = this.pos.get_cashier().id;
                this.lock_timestamp = new Date();

                // Disable further modifications
                this.trigger('change', this);
            }
        }
    });
});
```

#### **Testing Strategy**
- Unit tests for discount approval requirements
- Supervisor approval workflow tests
- Cashier-only finalization tests
- Cost price visibility tests
- Transaction locking tests

#### **Estimated Effort:** Large (5-6 weeks)
#### **Acceptance Criteria:**
- ✅ Discounts > 10% require supervisor approval
- ✅ Price overrides require supervisor approval
- ✅ Only cashiers can finalize transactions
- ✅ Cost prices hidden from non-supervisors
- ✅ Supervisor can delete items with reason logging
- ✅ Transaction locking prevents further modifications
- ✅ Audit trail captures all critical changes

---

### **MODULE 4: `vetlane_pos_multi_branch_stock` - Multi-Branch Stock Display**

#### **Module Overview**
**Purpose:** Implements multi-branch stock display and fulfillment selection in POS (FR-POS5)

**Dependencies:** `vetlane_base`, `vetlane_multi_branch`, `point_of_sale`

#### **Key Models & Fields**

##### **1. Extended POS Config**
```python
class PosConfig(models.Model):
    _inherit = 'pos.config'

    show_multi_branch_stock = fields.Boolean(
        string='Show Multi-Branch Stock',
        default=True,
        help='Display stock levels from all branches in POS'
    )

    allow_cross_branch_fulfillment = fields.Boolean(
        string='Allow Cross-Branch Fulfillment',
        default=False,
        help='Allow selling from other branch stock'
    )

    default_fulfillment_branch_id = fields.Many2one(
        'res.company',
        string='Default Fulfillment Branch',
        default=lambda self: self.env.company
    )

    visible_branch_ids = fields.Many2many(
        'res.company',
        string='Visible Branches',
        help='Branches whose stock should be visible in this POS'
    )
```

##### **2. Extended Product Product**
```python
class ProductProduct(models.Model):
    _inherit = 'product.product'

    def get_multi_branch_stock(self, branch_ids=None):
        """Get stock quantities across multiple branches"""
        if not branch_ids:
            branch_ids = self.env['res.company'].search([]).ids

        stock_data = {}
        for branch_id in branch_ids:
            branch = self.env['res.company'].browse(branch_id)
            warehouses = self.env['stock.warehouse'].search([
                ('branch_id', '=', branch_id)
            ])

            total_qty = 0
            for warehouse in warehouses:
                quants = self.env['stock.quant'].search([
                    ('product_id', '=', self.id),
                    ('location_id', 'child_of', warehouse.lot_stock_id.id)
                ])
                total_qty += sum(quants.mapped('quantity'))

            stock_data[branch_id] = {
                'branch_name': branch.name,
                'quantity': total_qty,
                'available': total_qty > 0
            }

        return stock_data

    @api.model
    def get_pos_stock_data(self, product_ids, pos_config_id):
        """Get stock data formatted for POS"""
        pos_config = self.env['pos.config'].browse(pos_config_id)
        branch_ids = pos_config.visible_branch_ids.ids or [pos_config.default_fulfillment_branch_id.id]

        stock_data = {}
        for product_id in product_ids:
            product = self.browse(product_id)
            stock_data[product_id] = product.get_multi_branch_stock(branch_ids)

        return stock_data
```

##### **3. POS Order Line Extension**
```python
class PosOrderLine(models.Model):
    _inherit = 'pos.order.line'

    fulfillment_branch_id = fields.Many2one(
        'res.company',
        string='Fulfillment Branch',
        help='Branch from which this item will be fulfilled'
    )

    fulfillment_warehouse_id = fields.Many2one(
        'stock.warehouse',
        string='Fulfillment Warehouse',
        help='Warehouse from which this item will be fulfilled'
    )

    stock_available_at_fulfillment = fields.Float(
        string='Available Stock',
        compute='_compute_available_stock'
    )

    @api.depends('product_id', 'fulfillment_warehouse_id')
    def _compute_available_stock(self):
        for line in self:
            if line.product_id and line.fulfillment_warehouse_id:
                quants = self.env['stock.quant'].search([
                    ('product_id', '=', line.product_id.id),
                    ('location_id', 'child_of', line.fulfillment_warehouse_id.lot_stock_id.id)
                ])
                line.stock_available_at_fulfillment = sum(quants.mapped('quantity'))
            else:
                line.stock_available_at_fulfillment = 0.0

    @api.constrains('qty', 'stock_available_at_fulfillment')
    def _check_stock_availability(self):
        for line in self:
            if line.fulfillment_warehouse_id and line.qty > line.stock_available_at_fulfillment:
                raise ValidationError(
                    f"Insufficient stock for {line.product_id.name} at {line.fulfillment_branch_id.name}. "
                    f"Available: {line.stock_available_at_fulfillment}, Requested: {line.qty}"
                )
```

#### **JavaScript Extensions**
```javascript
// pos_multi_branch_stock.js
odoo.define('vetlane_pos_multi_branch_stock.models', function (require) {
    "use strict";

    var models = require('point_of_sale.models');
    var core = require('web.core');
    var _t = core._t;

    // Load multi-branch stock data
    models.load_fields('product.product', ['multi_branch_stock_data']);

    // Extend POS Model
    var _super_posmodel = models.PosModel.prototype;
    models.PosModel = models.PosModel.extend({

        initialize: function(session, attributes) {
            _super_posmodel.initialize.call(this, session, attributes);
            this.multi_branch_stock = {};
        },

        after_load_server_data: function() {
            var self = this;
            return _super_posmodel.after_load_server_data.call(this).then(function() {
                return self.load_multi_branch_stock();
            });
        },

        load_multi_branch_stock: function() {
            var self = this;
            var product_ids = this.db.get_product_ids();

            return this.rpc({
                model: 'product.product',
                method: 'get_pos_stock_data',
                args: [product_ids, this.config.id]
            }).then(function(stock_data) {
                self.multi_branch_stock = stock_data;
                self.trigger('multi_branch_stock_loaded');
            });
        },

        get_product_stock_by_branch: function(product_id) {
            return this.multi_branch_stock[product_id] || {};
        },

        get_total_available_stock: function(product_id) {
            var stock_data = this.get_product_stock_by_branch(product_id);
            var total = 0;

            for (var branch_id in stock_data) {
                total += stock_data[branch_id].quantity || 0;
            }

            return total;
        }
    });

    // Extend Orderline
    var _super_orderline = models.Orderline.prototype;
    models.Orderline = models.Orderline.extend({

        initialize: function(attr, options) {
            _super_orderline.initialize.call(this, attr, options);
            this.fulfillment_branch_id = this.pos.config.default_fulfillment_branch_id;
        },

        set_fulfillment_branch: function(branch_id) {
            this.fulfillment_branch_id = branch_id;
            this.trigger('change', this);
        },

        get_available_stock_at_branch: function(branch_id) {
            var stock_data = this.pos.get_product_stock_by_branch(this.product.id);
            return stock_data[branch_id] ? stock_data[branch_id].quantity : 0;
        }
    });
});
```

#### **Testing Strategy**
- Unit tests for multi-branch stock calculation
- POS stock display tests
- Cross-branch fulfillment tests
- Stock availability validation tests

#### **Estimated Effort:** Medium (3-4 weeks)
#### **Acceptance Criteria:**
- ✅ Stock levels displayed for all configured branches
- ✅ Cross-branch fulfillment selection works
- ✅ Stock availability validation prevents overselling
- ✅ Real-time stock updates in POS interface

---

### **MODULE 5: `vetlane_pos_returns_exchange` - Returns & Exchange Workflow**

#### **Module Overview**
**Purpose:** Implements advanced returns and exchange workflow without deleting original records (FR-POS3)

**Dependencies:** `vetlane_base`, `point_of_sale`

#### **Key Models & Fields**

##### **1. POS Return/Exchange Model**
```python
class PosReturnExchange(models.Model):
    _name = 'pos.return.exchange'
    _description = 'POS Return/Exchange Transaction'
    _inherit = ['mail.thread', 'vetlane.audit.mixin']
    _order = 'create_date desc'

    name = fields.Char(
        string='Return/Exchange Reference',
        default=lambda self: VetlaneUtils.generate_reference_number('PRE'),
        required=True
    )

    original_order_id = fields.Many2one(
        'pos.order',
        string='Original Order',
        required=True
    )

    return_order_id = fields.Many2one(
        'pos.order',
        string='Return Order',
        help='New order created for the return/exchange'
    )

    transaction_type = fields.Selection([
        ('return', 'Return'),
        ('exchange', 'Exchange')
    ], required=True, default='return')

    reason = fields.Selection([
        ('defective', 'Defective Product'),
        ('wrong_item', 'Wrong Item'),
        ('customer_change', 'Customer Changed Mind'),
        ('damaged', 'Damaged in Transit'),
        ('expired', 'Expired Product'),
        ('other', 'Other')
    ], required=True)

    reason_notes = fields.Text('Reason Notes')

    state = fields.Selection([
        ('draft', 'Draft'),
        ('approved', 'Approved'),
        ('processed', 'Processed'),
        ('cancelled', 'Cancelled')
    ], default='draft', tracking=True)

    return_line_ids = fields.One2many(
        'pos.return.exchange.line',
        'return_exchange_id',
        string='Return Lines'
    )

    total_return_amount = fields.Float(
        string='Total Return Amount',
        compute='_compute_total_return_amount'
    )

    refund_method = fields.Selection([
        ('cash', 'Cash Refund'),
        ('store_credit', 'Store Credit'),
        ('exchange', 'Exchange for Other Items')
    ], string='Refund Method')

    processed_by = fields.Many2one(
        'res.users',
        string='Processed By'
    )

    approved_by = fields.Many2one(
        'res.users',
        string='Approved By'
    )

    @api.depends('return_line_ids.return_amount')
    def _compute_total_return_amount(self):
        for record in self:
            record.total_return_amount = sum(record.return_line_ids.mapped('return_amount'))

    def action_approve(self):
        """Approve the return/exchange"""
        if not self.env.user.has_group('vetlane_base.group_vetlane_supervisor'):
            raise UserError("Only supervisors can approve returns/exchanges")

        self.write({
            'state': 'approved',
            'approved_by': self.env.user.id
        })

    def action_process(self):
        """Process the return/exchange"""
        if self.state != 'approved':
            raise UserError("Return/exchange must be approved before processing")

        # Create return order
        return_order = self._create_return_order()

        self.write({
            'state': 'processed',
            'return_order_id': return_order.id,
            'processed_by': self.env.user.id
        })

        return return_order

    def _create_return_order(self):
        """Create a new POS order for the return"""
        order_vals = {
            'session_id': self.original_order_id.session_id.id,
            'partner_id': self.original_order_id.partner_id.id,
            'lines': [(0, 0, {
                'product_id': line.product_id.id,
                'qty': -line.return_qty,  # Negative quantity for return
                'price_unit': line.original_price,
                'discount': 0,
            }) for line in self.return_line_ids],
            'amount_tax': -self.original_order_id.amount_tax,
            'amount_total': -self.total_return_amount,
            'amount_paid': -self.total_return_amount,
            'note': f'Return/Exchange for order {self.original_order_id.name}. Reason: {self.reason}'
        }

        return_order = self.env['pos.order'].create(order_vals)
        return_order.action_pos_order_paid()

        return return_order

class PosReturnExchangeLine(models.Model):
    _name = 'pos.return.exchange.line'
    _description = 'POS Return/Exchange Line'

    return_exchange_id = fields.Many2one(
        'pos.return.exchange',
        string='Return/Exchange',
        required=True,
        ondelete='cascade'
    )

    original_line_id = fields.Many2one(
        'pos.order.line',
        string='Original Order Line',
        required=True
    )

    product_id = fields.Many2one(
        'product.product',
        string='Product',
        related='original_line_id.product_id',
        readonly=True
    )

    original_qty = fields.Float(
        string='Original Quantity',
        related='original_line_id.qty',
        readonly=True
    )

    original_price = fields.Float(
        string='Original Price',
        related='original_line_id.price_unit',
        readonly=True
    )

    return_qty = fields.Float(
        string='Return Quantity',
        required=True,
        default=1.0
    )

    return_amount = fields.Float(
        string='Return Amount',
        compute='_compute_return_amount'
    )

    condition = fields.Selection([
        ('new', 'New/Unopened'),
        ('used', 'Used'),
        ('damaged', 'Damaged'),
        ('defective', 'Defective')
    ], string='Item Condition', required=True)

    @api.depends('return_qty', 'original_price')
    def _compute_return_amount(self):
        for line in self:
            line.return_amount = line.return_qty * line.original_price

    @api.constrains('return_qty', 'original_qty')
    def _check_return_qty(self):
        for line in self:
            if line.return_qty > line.original_qty:
                raise ValidationError(
                    f"Cannot return more than original quantity. "
                    f"Original: {line.original_qty}, Return: {line.return_qty}"
                )
```

#### **UI/UX Implementation**
```xml
<!-- Return/Exchange Form View -->
<record id="view_pos_return_exchange_form" model="ir.ui.view">
    <field name="name">pos.return.exchange.form</field>
    <field name="model">pos.return.exchange</field>
    <field name="arch" type="xml">
        <form string="POS Return/Exchange">
            <header>
                <button name="action_approve" type="object" string="Approve"
                        states="draft" class="btn-primary"
                        groups="vetlane_base.group_vetlane_supervisor"/>
                <button name="action_process" type="object" string="Process"
                        states="approved" class="btn-success"/>
                <field name="state" widget="statusbar"/>
            </header>
            <sheet>
                <div class="oe_title">
                    <h1><field name="name"/></h1>
                </div>
                <group>
                    <group>
                        <field name="original_order_id"/>
                        <field name="transaction_type"/>
                        <field name="reason"/>
                    </group>
                    <group>
                        <field name="refund_method"/>
                        <field name="total_return_amount"/>
                        <field name="processed_by" readonly="1"/>
                    </group>
                </group>
                <group>
                    <field name="reason_notes"/>
                </group>
                <notebook>
                    <page string="Return Lines">
                        <field name="return_line_ids">
                            <tree editable="bottom">
                                <field name="product_id" readonly="1"/>
                                <field name="original_qty" readonly="1"/>
                                <field name="original_price" readonly="1"/>
                                <field name="return_qty"/>
                                <field name="return_amount" readonly="1"/>
                                <field name="condition"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </sheet>
            <div class="oe_chatter">
                <field name="message_follower_ids"/>
                <field name="message_ids"/>
            </div>
        </form>
    </field>
</record>
```

#### **Testing Strategy**
- Unit tests for return/exchange creation
- Approval workflow tests
- Return order generation tests
- Quantity validation tests

#### **Estimated Effort:** Medium (4-5 weeks)
#### **Acceptance Criteria:**
- ✅ Returns/exchanges can be created from original orders
- ✅ Supervisor approval required for processing
- ✅ Original orders remain intact (not deleted)
- ✅ Return orders created with negative quantities
- ✅ Multiple refund methods supported
- ✅ Proper audit trail maintained

---

### **MODULE 6: `vetlane_pos_cash_management` - Cash Handling & Reconciliation**

#### **Module Overview**
**Purpose:** Implements custom cash payment method, end-of-day reconciliation, and delivery fee editing (FR-POS9, FR-POS10, FR-POS12)

**Dependencies:** `vetlane_base`, `point_of_sale`

#### **Key Models & Fields**

##### **1. Cash Deposit Model**
```python
class PosCashDeposit(models.Model):
    _name = 'pos.cash.deposit'
    _description = 'POS Cash Deposit'
    _inherit = ['mail.thread', 'vetlane.audit.mixin']
    _order = 'create_date desc'

    name = fields.Char(
        string='Deposit Reference',
        default=lambda self: VetlaneUtils.generate_reference_number('PCD'),
        required=True
    )

    session_id = fields.Many2one(
        'pos.session',
        string='POS Session',
        required=True
    )

    cashier_id = fields.Many2one(
        'res.users',
        string='Cashier',
        default=lambda self: self.env.user,
        required=True
    )

    deposit_amount = fields.Float(
        string='Deposit Amount',
        required=True
    )

    remaining_cash = fields.Float(
        string='Remaining Cash with Cashier'
    )

    deposit_date = fields.Datetime(
        string='Deposit Date',
        default=fields.Datetime.now,
        required=True
    )

    notes = fields.Text('Notes')

    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('deposited', 'Deposited')
    ], default='draft', tracking=True)

    def action_confirm(self):
        """Confirm the cash deposit"""
        self.state = 'confirmed'

    def action_deposit(self):
        """Mark as deposited"""
        self.state = 'deposited'

        # Update session cash balance
        self.session_id.cash_register_balance_end_real -= self.deposit_amount

class PosEndOfDayReconciliation(models.Model):
    _name = 'pos.eod.reconciliation'
    _description = 'POS End of Day Reconciliation'
    _inherit = ['mail.thread', 'vetlane.audit.mixin']

    name = fields.Char(
        string='EOD Reference',
        default=lambda self: VetlaneUtils.generate_reference_number('EOD'),
        required=True
    )

    session_id = fields.Many2one(
        'pos.session',
        string='POS Session',
        required=True
    )

    reconciliation_date = fields.Date(
        string='Reconciliation Date',
        default=fields.Date.today,
        required=True
    )

    expected_cash = fields.Float(
        string='Expected Cash',
        compute='_compute_expected_cash'
    )

    actual_cash = fields.Float(
        string='Actual Cash Counted',
        required=True
    )

    cash_difference = fields.Float(
        string='Cash Difference',
        compute='_compute_cash_difference'
    )

    total_sales = fields.Float(
        string='Total Sales',
        compute='_compute_totals'
    )

    total_returns = fields.Float(
        string='Total Returns',
        compute='_compute_totals'
    )

    cash_deposits = fields.Float(
        string='Cash Deposits',
        compute='_compute_cash_deposits'
    )

    reconciliation_notes = fields.Text('Reconciliation Notes')

    state = fields.Selection([
        ('draft', 'Draft'),
        ('reconciled', 'Reconciled'),
        ('discrepancy', 'Discrepancy')
    ], default='draft', tracking=True)

    @api.depends('session_id')
    def _compute_expected_cash(self):
        for record in self:
            if record.session_id:
                # Calculate expected cash based on session data
                cash_payments = record.session_id.order_ids.filtered(
                    lambda o: any(p.payment_method_id.is_cash_count for p in o.payment_ids)
                )
                record.expected_cash = sum(cash_payments.mapped('amount_total'))
            else:
                record.expected_cash = 0.0

    @api.depends('expected_cash', 'actual_cash')
    def _compute_cash_difference(self):
        for record in self:
            record.cash_difference = record.actual_cash - record.expected_cash

    @api.depends('session_id')
    def _compute_totals(self):
        for record in self:
            if record.session_id:
                orders = record.session_id.order_ids
                record.total_sales = sum(orders.filtered(lambda o: o.amount_total > 0).mapped('amount_total'))
                record.total_returns = sum(orders.filtered(lambda o: o.amount_total < 0).mapped('amount_total'))
            else:
                record.total_sales = 0.0
                record.total_returns = 0.0

    @api.depends('session_id')
    def _compute_cash_deposits(self):
        for record in self:
            if record.session_id:
                deposits = self.env['pos.cash.deposit'].search([
                    ('session_id', '=', record.session_id.id),
                    ('state', '=', 'deposited')
                ])
                record.cash_deposits = sum(deposits.mapped('deposit_amount'))
            else:
                record.cash_deposits = 0.0

    def action_reconcile(self):
        """Reconcile the cash"""
        if abs(self.cash_difference) <= 5.0:  # Allow ₦5 tolerance
            self.state = 'reconciled'
        else:
            self.state = 'discrepancy'
            # Send notification for discrepancy
            self.message_post(
                body=f"Cash discrepancy detected: ₦{self.cash_difference}. Please investigate."
            )
```

##### **2. Extended POS Payment Method**
```python
class PosPaymentMethod(models.Model):
    _inherit = 'pos.payment.method'

    is_cash_with_cashier = fields.Boolean(
        string='Cash with Cashier',
        default=False,
        help='Special cash payment method that stays with cashier'
    )

class PosPayment(models.Model):
    _inherit = 'pos.payment'

    delivery_fee_amount = fields.Float(
        string='Delivery Fee',
        default=0.0
    )

    is_delivery_fee_editable = fields.Boolean(
        string='Delivery Fee Editable',
        compute='_compute_delivery_fee_editable'
    )

    @api.depends('payment_method_id')
    def _compute_delivery_fee_editable(self):
        for payment in self:
            # Only supervisors can edit delivery fees
            payment.is_delivery_fee_editable = self.env.user.has_group('vetlane_base.group_vetlane_supervisor')
```

#### **JavaScript Extensions**
```javascript
// pos_cash_management.js
odoo.define('vetlane_pos_cash_management.models', function (require) {
    "use strict";

    var models = require('point_of_sale.models');
    var screens = require('point_of_sale.screens');
    var core = require('web.core');
    var _t = core._t;

    // Extend Payment Screen
    var PaymentScreenWidget = screens.PaymentScreenWidget;
    screens.PaymentScreenWidget = screens.PaymentScreenWidget.extend({

        init: function(parent, options) {
            this._super(parent, options);
            this.delivery_fee_editable = false;
        },

        renderElement: function() {
            this._super();
            this.setup_delivery_fee_editing();
        },

        setup_delivery_fee_editing: function() {
            var self = this;
            var user = this.pos.get_cashier();

            // Check if user can edit delivery fees
            if (user.groups_id.includes(this.pos.config.supervisor_group_id)) {
                this.delivery_fee_editable = true;

                // Add delivery fee editing controls
                this.$('.delivery-fee-edit').on('click', function() {
                    self.show_delivery_fee_popup();
                });
            }
        },

        show_delivery_fee_popup: function() {
            var self = this;
            this.gui.show_popup('number', {
                'title': _t('Edit Delivery Fee'),
                'value': this.get_current_delivery_fee(),
                'confirm': function(value) {
                    self.set_delivery_fee(value);
                }
            });
        },

        get_current_delivery_fee: function() {
            var order = this.pos.get_order();
            var delivery_line = order.get_orderlines().find(function(line) {
                return line.product.is_delivery_product;
            });
            return delivery_line ? delivery_line.get_unit_price() : 0;
        },

        set_delivery_fee: function(amount) {
            var order = this.pos.get_order();
            var delivery_line = order.get_orderlines().find(function(line) {
                return line.product.is_delivery_product;
            });

            if (delivery_line) {
                delivery_line.set_unit_price(amount);
            }
        }
    });

    // Cash Deposit Functionality
    var CashDepositPopup = PopupWidget.extend({
        template: 'CashDepositPopup',

        show: function(options) {
            this._super(options);
            this.deposit_amount = 0;
            this.remaining_cash = 0;
        },

        click_confirm: function() {
            var self = this;

            return this.pos.rpc({
                model: 'pos.cash.deposit',
                method: 'create',
                args: [{
                    session_id: this.pos.pos_session.id,
                    deposit_amount: this.deposit_amount,
                    remaining_cash: this.remaining_cash
                }]
            }).then(function(deposit_id) {
                self.gui.show_popup('alert', {
                    title: _t('Cash Deposit'),
                    body: _t('Cash deposit recorded successfully.')
                });
                self.gui.close_popup();
            });
        }
    });

    gui.define_popup({name:'cash_deposit', widget: CashDepositPopup});
});
```

#### **Testing Strategy**
- Unit tests for cash deposit creation
- EOD reconciliation calculation tests
- Delivery fee editing permission tests
- Cash difference tolerance tests

#### **Estimated Effort:** Medium (3-4 weeks)
#### **Acceptance Criteria:**
- ✅ Custom "Cash with Cashier" payment method works
- ✅ End-of-day reconciliation wizard functions properly
- ✅ Cash deposits can be recorded and tracked
- ✅ Delivery fees can be edited by supervisors only
- ✅ Cash discrepancies are flagged and reported

---

### **MODULE 7: `vetlane_pos_user_management` - User Management & Customer Search**

#### **Module Overview**
**Purpose:** Implements bulk user import via CSV and live customer search with inline creation (FR-POS6, FR-POS7)

**Dependencies:** `vetlane_base`, `point_of_sale`

#### **Key Models & Fields**

##### **1. Bulk User Import Wizard**
```python
class BulkUserImportWizard(models.TransientModel):
    _name = 'bulk.user.import.wizard'
    _description = 'Bulk User Import Wizard'

    csv_file = fields.Binary(
        string='CSV File',
        required=True,
        help='CSV file containing user data'
    )

    filename = fields.Char('Filename')

    user_type = fields.Selection([
        ('cashier', 'Cashier'),
        ('supervisor', 'Supervisor'),
        ('inventory_lead', 'Inventory Lead'),
        ('delivery_agent', 'Delivery Agent')
    ], required=True, default='cashier')

    default_branch_id = fields.Many2one(
        'res.company',
        string='Default Branch',
        required=True,
        default=lambda self: self.env.company
    )

    import_results = fields.Text(
        string='Import Results',
        readonly=True
    )

    def action_import_users(self):
        """Import users from CSV file"""
        if not self.csv_file:
            raise UserError("Please upload a CSV file")

        import base64
        import csv
        import io

        # Decode CSV file
        csv_data = base64.b64decode(self.csv_file)
        csv_file = io.StringIO(csv_data.decode('utf-8'))
        csv_reader = csv.DictReader(csv_file)

        results = []
        success_count = 0
        error_count = 0

        # Get the appropriate group based on user type
        group_mapping = {
            'cashier': 'vetlane_base.group_vetlane_cashier',
            'supervisor': 'vetlane_base.group_vetlane_supervisor',
            'inventory_lead': 'vetlane_base.group_vetlane_inventory_lead',
            'delivery_agent': 'vetlane_base.group_vetlane_delivery_agent'
        }

        target_group = self.env.ref(group_mapping[self.user_type])

        for row in csv_reader:
            try:
                # Validate required fields
                if not row.get('name') or not row.get('login'):
                    results.append(f"Error: Missing name or login for row {csv_reader.line_num}")
                    error_count += 1
                    continue

                # Check if user already exists
                existing_user = self.env['res.users'].search([('login', '=', row['login'])])
                if existing_user:
                    results.append(f"Warning: User {row['login']} already exists, skipping")
                    continue

                # Create user
                user_vals = {
                    'name': row['name'],
                    'login': row['login'],
                    'email': row.get('email', ''),
                    'phone': row.get('phone', ''),
                    'company_id': self.default_branch_id.id,
                    'company_ids': [(6, 0, [self.default_branch_id.id])],
                    'groups_id': [(6, 0, [target_group.id])]
                }

                # Set default password if provided
                if row.get('password'):
                    user_vals['password'] = row['password']

                user = self.env['res.users'].create(user_vals)
                results.append(f"Success: Created user {user.name} ({user.login})")
                success_count += 1

            except Exception as e:
                results.append(f"Error: Failed to create user for row {csv_reader.line_num}: {str(e)}")
                error_count += 1

        # Update results
        summary = f"Import completed: {success_count} successful, {error_count} errors\n\n"
        self.import_results = summary + "\n".join(results)

        return {
            'type': 'ir.actions.act_window',
            'res_model': 'bulk.user.import.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {'show_results': True}
        }
```

##### **2. Extended POS Config for Customer Search**
```python
class PosConfig(models.Model):
    _inherit = 'pos.config'

    enable_live_customer_search = fields.Boolean(
        string='Enable Live Customer Search',
        default=True,
        help='Enable real-time customer search in POS'
    )

    customer_search_limit = fields.Integer(
        string='Customer Search Limit',
        default=50,
        help='Maximum number of customers to return in search'
    )

    allow_customer_creation = fields.Boolean(
        string='Allow Customer Creation',
        default=True,
        help='Allow creating new customers from POS'
    )

class ResPartner(models.Model):
    _inherit = 'res.partner'

    @api.model
    def pos_search_customers(self, query, limit=50):
        """Search customers for POS with enhanced matching"""
        if not query or len(query) < 2:
            return []

        # Search by name, phone, email
        domain = [
            '|', '|',
            ('name', 'ilike', query),
            ('phone', 'ilike', query),
            ('email', 'ilike', query)
        ]

        customers = self.search(domain, limit=limit)

        # Format results for POS
        result = []
        for customer in customers:
            result.append({
                'id': customer.id,
                'name': customer.name,
                'phone': customer.phone or '',
                'email': customer.email or '',
                'address': customer.contact_address or '',
                'vat': customer.vat or ''
            })

        return result

    @api.model
    def pos_create_customer(self, customer_data):
        """Create customer from POS"""
        # Validate required fields
        if not customer_data.get('name'):
            raise UserError("Customer name is required")

        # Check for duplicates
        if customer_data.get('phone'):
            existing = self.search([('phone', '=', customer_data['phone'])])
            if existing:
                raise UserError(f"Customer with phone {customer_data['phone']} already exists")

        # Create customer
        customer = self.create({
            'name': customer_data['name'],
            'phone': customer_data.get('phone', ''),
            'email': customer_data.get('email', ''),
            'street': customer_data.get('street', ''),
            'city': customer_data.get('city', ''),
            'is_company': False,
            'customer_rank': 1
        })

        return {
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone or '',
            'email': customer.email or '',
            'address': customer.contact_address or ''
        }
```

#### **JavaScript Extensions**
```javascript
// pos_user_management.js
odoo.define('vetlane_pos_user_management.screens', function (require) {
    "use strict";

    var screens = require('point_of_sale.screens');
    var gui = require('point_of_sale.gui');
    var core = require('web.core');
    var _t = core._t;

    // Enhanced Customer Search Screen
    var CustomerSearchScreenWidget = screens.ScreenWidget.extend({
        template: 'CustomerSearchScreenWidget',

        init: function(parent, options) {
            this._super(parent, options);
            this.search_timeout = null;
            this.search_results = [];
        },

        show: function() {
            this._super();
            this.setup_search();
        },

        setup_search: function() {
            var self = this;

            this.$('.customer-search-input').on('input', function() {
                clearTimeout(self.search_timeout);
                var query = $(this).val();

                if (query.length >= 2) {
                    self.search_timeout = setTimeout(function() {
                        self.search_customers(query);
                    }, 300);
                } else {
                    self.clear_results();
                }
            });

            this.$('.create-customer-btn').on('click', function() {
                self.show_create_customer_popup();
            });
        },

        search_customers: function(query) {
            var self = this;

            return this.pos.rpc({
                model: 'res.partner',
                method: 'pos_search_customers',
                args: [query, this.pos.config.customer_search_limit]
            }).then(function(results) {
                self.search_results = results;
                self.render_search_results();
            });
        },

        render_search_results: function() {
            var self = this;
            var $results = this.$('.search-results');
            $results.empty();

            if (this.search_results.length === 0) {
                $results.append('<div class="no-results">No customers found</div>');
                return;
            }

            this.search_results.forEach(function(customer) {
                var $item = $('<div class="customer-item">');
                $item.html(`
                    <div class="customer-name">${customer.name}</div>
                    <div class="customer-details">${customer.phone} | ${customer.email}</div>
                    <div class="customer-address">${customer.address}</div>
                `);

                $item.on('click', function() {
                    self.select_customer(customer);
                });

                $results.append($item);
            });
        },

        select_customer: function(customer) {
            var order = this.pos.get_order();
            var partner = this.pos.db.get_partner_by_id(customer.id);

            if (!partner) {
                // Add customer to local database
                this.pos.db.add_partners([customer]);
                partner = this.pos.db.get_partner_by_id(customer.id);
            }

            order.set_client(partner);
            this.gui.back();
        },

        show_create_customer_popup: function() {
            var self = this;

            this.gui.show_popup('customer_create', {
                title: _t('Create New Customer'),
                confirm: function(customer_data) {
                    self.create_customer(customer_data);
                }
            });
        },

        create_customer: function(customer_data) {
            var self = this;

            return this.pos.rpc({
                model: 'res.partner',
                method: 'pos_create_customer',
                args: [customer_data]
            }).then(function(customer) {
                // Add to local database
                self.pos.db.add_partners([customer]);

                // Select the new customer
                self.select_customer(customer);

                self.gui.show_popup('alert', {
                    title: _t('Success'),
                    body: _t('Customer created successfully')
                });
            }).catch(function(error) {
                self.gui.show_popup('error', {
                    title: _t('Error'),
                    body: error.message || _t('Failed to create customer')
                });
            });
        },

        clear_results: function() {
            this.$('.search-results').empty();
            this.search_results = [];
        }
    });

    gui.define_screen({name:'customer_search', widget: CustomerSearchScreenWidget});
});
```

#### **UI/UX Implementation**
```xml
<!-- Bulk User Import Wizard View -->
<record id="view_bulk_user_import_wizard" model="ir.ui.view">
    <field name="name">bulk.user.import.wizard.form</field>
    <field name="model">bulk.user.import.wizard</field>
    <field name="arch" type="xml">
        <form string="Bulk User Import">
            <group>
                <field name="csv_file" filename="filename"/>
                <field name="filename" invisible="1"/>
                <field name="user_type"/>
                <field name="default_branch_id"/>
            </group>
            <group string="Import Results" attrs="{'invisible': [('import_results', '=', False)]}">
                <field name="import_results" nolabel="1"/>
            </group>
            <footer>
                <button string="Import Users" name="action_import_users" type="object" class="btn-primary"/>
                <button string="Cancel" class="btn-secondary" special="cancel"/>
            </footer>
        </form>
    </field>
</record>

<!-- Menu Item -->
<menuitem id="menu_bulk_user_import"
          name="Bulk User Import"
          parent="base.menu_users"
          action="action_bulk_user_import_wizard"
          groups="vetlane_base.group_vetlane_admin"/>
```

#### **Testing Strategy**
- Unit tests for CSV user import
- Customer search functionality tests
- Customer creation validation tests
- Permission-based access tests

#### **Estimated Effort:** Medium (3-4 weeks)
#### **Acceptance Criteria:**
- ✅ CSV bulk user import works correctly
- ✅ Live customer search functions in real-time
- ✅ New customers can be created inline
- ✅ Duplicate customer prevention works
- ✅ Search results are properly formatted
- ✅ User role assignment works correctly

---

## **PART 3: INVENTORY & PROCUREMENT MODULES**

### **MODULE 8: `vetlane_inventory_lot_tracking` - Enhanced Lot Management**

#### **Module Overview**
**Purpose:** Implements mandatory lot/expiry tracking with multi-lot entry per PO line (FR-INV2, FR-INV3)

**Dependencies:** `vetlane_base`, `stock`, `purchase`

#### **Key Models & Fields**

##### **1. Extended Purchase Order Line**
```python
class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'
    _inherit = ['purchase.order.line', 'vetlane.audit.mixin']

    lot_tracking_required = fields.Boolean(
        string='Lot Tracking Required',
        compute='_compute_lot_tracking_required',
        store=True
    )

    lot_entry_ids = fields.One2many(
        'purchase.lot.entry',
        'po_line_id',
        string='Lot Entries'
    )

    total_lot_qty = fields.Float(
        string='Total Lot Quantity',
        compute='_compute_total_lot_qty'
    )

    lot_qty_difference = fields.Float(
        string='Quantity Difference',
        compute='_compute_lot_qty_difference'
    )

    @api.depends('product_id.tracking')
    def _compute_lot_tracking_required(self):
        for line in self:
            line.lot_tracking_required = line.product_id.tracking in ['lot', 'serial']

    @api.depends('lot_entry_ids.quantity')
    def _compute_total_lot_qty(self):
        for line in self:
            line.total_lot_qty = sum(line.lot_entry_ids.mapped('quantity'))

    @api.depends('product_qty', 'total_lot_qty')
    def _compute_lot_qty_difference(self):
        for line in self:
            line.lot_qty_difference = line.product_qty - line.total_lot_qty

    @api.constrains('product_qty', 'total_lot_qty', 'lot_tracking_required')
    def _check_lot_quantities(self):
        for line in self:
            if line.lot_tracking_required and line.state in ['purchase', 'done']:
                if abs(line.lot_qty_difference) > 0.01:  # Allow small rounding differences
                    raise ValidationError(
                        f"Total lot quantities ({line.total_lot_qty}) must equal "
                        f"order quantity ({line.product_qty}) for {line.product_id.name}"
                    )

    def action_add_lot_entry(self):
        """Open wizard to add lot entries"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Add Lot Entry',
            'res_model': 'purchase.lot.entry.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_po_line_id': self.id,
                'default_product_id': self.product_id.id,
                'remaining_qty': self.lot_qty_difference
            }
        }

class PurchaseLotEntry(models.Model):
    _name = 'purchase.lot.entry'
    _description = 'Purchase Order Lot Entry'
    _rec_name = 'lot_name'

    po_line_id = fields.Many2one(
        'purchase.order.line',
        string='Purchase Order Line',
        required=True,
        ondelete='cascade'
    )

    product_id = fields.Many2one(
        'product.product',
        string='Product',
        related='po_line_id.product_id',
        readonly=True
    )

    lot_name = fields.Char(
        string='Lot/Serial Number',
        required=True
    )

    quantity = fields.Float(
        string='Quantity',
        required=True,
        default=1.0
    )

    manufacturing_date = fields.Date(
        string='Manufacturing Date'
    )

    expiry_date = fields.Date(
        string='Expiry Date',
        required=True
    )

    supplier_lot_ref = fields.Char(
        string='Supplier Lot Reference'
    )

    notes = fields.Text('Notes')

    stock_lot_id = fields.Many2one(
        'stock.lot',
        string='Stock Lot',
        readonly=True,
        help='Generated stock lot record'
    )

    @api.constrains('expiry_date')
    def _check_expiry_date(self):
        for entry in self:
            if entry.expiry_date and entry.expiry_date <= fields.Date.today():
                raise ValidationError("Expiry date must be in the future")

    @api.constrains('lot_name', 'product_id')
    def _check_unique_lot(self):
        for entry in self:
            existing = self.search([
                ('lot_name', '=', entry.lot_name),
                ('product_id', '=', entry.product_id.id),
                ('id', '!=', entry.id)
            ])
            if existing:
                raise ValidationError(f"Lot {entry.lot_name} already exists for this product")

    def create_stock_lot(self):
        """Create corresponding stock lot record"""
        if not self.stock_lot_id:
            lot_vals = {
                'name': self.lot_name,
                'product_id': self.product_id.id,
                'expiration_date': self.expiry_date,
                'use_date': self.expiry_date,
                'removal_date': self.expiry_date,
                'alert_date': self.expiry_date - timedelta(days=30),  # 30 days before expiry
                'company_id': self.po_line_id.order_id.company_id.id
            }

            stock_lot = self.env['stock.lot'].create(lot_vals)
            self.stock_lot_id = stock_lot.id

        return self.stock_lot_id
```

##### **2. Lot Entry Wizard**
```python
class PurchaseLotEntryWizard(models.TransientModel):
    _name = 'purchase.lot.entry.wizard'
    _description = 'Purchase Lot Entry Wizard'

    po_line_id = fields.Many2one(
        'purchase.order.line',
        string='Purchase Order Line',
        required=True
    )

    product_id = fields.Many2one(
        'product.product',
        string='Product',
        required=True
    )

    remaining_qty = fields.Float(
        string='Remaining Quantity',
        readonly=True
    )

    lot_entry_ids = fields.One2many(
        'purchase.lot.entry.wizard.line',
        'wizard_id',
        string='Lot Entries'
    )

    def action_add_lots(self):
        """Add lot entries to purchase order line"""
        for line in self.lot_entry_ids:
            self.env['purchase.lot.entry'].create({
                'po_line_id': self.po_line_id.id,
                'lot_name': line.lot_name,
                'quantity': line.quantity,
                'manufacturing_date': line.manufacturing_date,
                'expiry_date': line.expiry_date,
                'supplier_lot_ref': line.supplier_lot_ref,
                'notes': line.notes
            })

        return {'type': 'ir.actions.act_window_close'}

class PurchaseLotEntryWizardLine(models.TransientModel):
    _name = 'purchase.lot.entry.wizard.line'
    _description = 'Purchase Lot Entry Wizard Line'

    wizard_id = fields.Many2one(
        'purchase.lot.entry.wizard',
        string='Wizard',
        required=True,
        ondelete='cascade'
    )

    lot_name = fields.Char(
        string='Lot/Serial Number',
        required=True
    )

    quantity = fields.Float(
        string='Quantity',
        required=True,
        default=1.0
    )

    manufacturing_date = fields.Date(
        string='Manufacturing Date'
    )

    expiry_date = fields.Date(
        string='Expiry Date',
        required=True
    )

    supplier_lot_ref = fields.Char(
        string='Supplier Lot Reference'
    )

    notes = fields.Text('Notes')
```

##### **3. Extended Stock Picking**
```python
class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def action_done(self):
        """Override to create stock lots from purchase lot entries"""
        result = super().action_done()

        # Create stock lots for purchase orders
        if self.origin and self.picking_type_id.code == 'incoming':
            po = self.env['purchase.order'].search([('name', '=', self.origin)], limit=1)
            if po:
                for line in po.order_line:
                    for lot_entry in line.lot_entry_ids:
                        if not lot_entry.stock_lot_id:
                            lot_entry.create_stock_lot()

                            # Update stock move lines with lot information
                            move_lines = self.move_line_ids.filtered(
                                lambda ml: ml.product_id == lot_entry.product_id
                            )

                            for move_line in move_lines:
                                if not move_line.lot_id and move_line.qty_done <= lot_entry.quantity:
                                    move_line.lot_id = lot_entry.stock_lot_id.id
                                    lot_entry.quantity -= move_line.qty_done
                                    break

        return result
```

#### **UI/UX Implementation**
```xml
<!-- Purchase Order Line Form View Extension -->
<record id="view_purchase_order_line_form_lot_tracking" model="ir.ui.view">
    <field name="name">purchase.order.line.form.lot.tracking</field>
    <field name="model">purchase.order.line</field>
    <field name="inherit_id" ref="purchase.purchase_order_line_form"/>
    <field name="arch" type="xml">
        <xpath expr="//field[@name='product_qty']" position="after">
            <field name="lot_tracking_required" invisible="1"/>
            <field name="total_lot_qty" attrs="{'invisible': [('lot_tracking_required', '=', False)]}"/>
            <field name="lot_qty_difference" attrs="{'invisible': [('lot_tracking_required', '=', False)]}"/>
        </xpath>
        <xpath expr="//field[@name='product_qty']" position="after">
            <button name="action_add_lot_entry" type="object" string="Add Lots"
                    attrs="{'invisible': [('lot_tracking_required', '=', False)]}"
                    class="btn-primary"/>
        </xpath>
    </field>
</record>

<!-- Lot Entry Tree View -->
<record id="view_purchase_lot_entry_tree" model="ir.ui.view">
    <field name="name">purchase.lot.entry.tree</field>
    <field name="model">purchase.lot.entry</field>
    <field name="arch" type="xml">
        <tree editable="bottom">
            <field name="lot_name"/>
            <field name="quantity"/>
            <field name="manufacturing_date"/>
            <field name="expiry_date"/>
            <field name="supplier_lot_ref"/>
            <field name="notes"/>
        </tree>
    </field>
</record>

<!-- Lot Entry Wizard Form -->
<record id="view_purchase_lot_entry_wizard_form" model="ir.ui.view">
    <field name="name">purchase.lot.entry.wizard.form</field>
    <field name="model">purchase.lot.entry.wizard</field>
    <field name="arch" type="xml">
        <form string="Add Lot Entries">
            <group>
                <field name="product_id" readonly="1"/>
                <field name="remaining_qty" readonly="1"/>
            </group>
            <field name="lot_entry_ids">
                <tree editable="bottom">
                    <field name="lot_name"/>
                    <field name="quantity"/>
                    <field name="manufacturing_date"/>
                    <field name="expiry_date"/>
                    <field name="supplier_lot_ref"/>
                    <field name="notes"/>
                </tree>
            </field>
            <footer>
                <button string="Add Lots" name="action_add_lots" type="object" class="btn-primary"/>
                <button string="Cancel" class="btn-secondary" special="cancel"/>
            </footer>
        </form>
    </field>
</record>
```

#### **Testing Strategy**
- Unit tests for lot entry creation and validation
- Purchase order lot quantity validation tests
- Stock lot creation from purchase lots tests
- Expiry date validation tests

#### **Estimated Effort:** Large (4-5 weeks)
#### **Acceptance Criteria:**
- ✅ Mandatory lot tracking enforced for tracked products
- ✅ Multi-lot entry per PO line supported
- ✅ Lot quantities must match order quantities
- ✅ Stock lots automatically created from purchase lots
- ✅ Expiry dates properly validated and tracked

---

### **MODULE 9: `vetlane_inventory_expiry_alerts` - Expiry Monitoring Dashboard**

#### **Module Overview**
**Purpose:** Implements expiry alert dashboard with cron job and color-coded urgency system (FR-INV4)

**Dependencies:** `vetlane_base`, `stock`

#### **Key Models & Fields**

##### **1. Expiry Alert Model**
```python
class InventoryExpiryAlert(models.Model):
    _name = 'inventory.expiry.alert'
    _description = 'Inventory Expiry Alert'
    _order = 'expiry_date asc, urgency_level desc'

    name = fields.Char(
        string='Alert Reference',
        default=lambda self: VetlaneUtils.generate_reference_number('EXP'),
        required=True
    )

    product_id = fields.Many2one(
        'product.product',
        string='Product',
        required=True
    )

    lot_id = fields.Many2one(
        'stock.lot',
        string='Lot/Serial Number',
        required=True
    )

    location_id = fields.Many2one(
        'stock.location',
        string='Location',
        required=True
    )

    warehouse_id = fields.Many2one(
        'stock.warehouse',
        string='Warehouse',
        compute='_compute_warehouse_id',
        store=True
    )

    branch_id = fields.Many2one(
        'res.company',
        string='Branch',
        related='warehouse_id.branch_id',
        store=True
    )

    current_qty = fields.Float(
        string='Current Quantity',
        compute='_compute_current_qty'
    )

    expiry_date = fields.Date(
        string='Expiry Date',
        related='lot_id.expiration_date',
        store=True
    )

    days_to_expiry = fields.Integer(
        string='Days to Expiry',
        compute='_compute_days_to_expiry',
        store=True
    )

    urgency_level = fields.Selection([
        ('expired', 'Expired'),
        ('critical', 'Critical (≤7 days)'),
        ('warning', 'Warning (≤30 days)'),
        ('normal', 'Normal (>30 days)')
    ], string='Urgency Level', compute='_compute_urgency_level', store=True)

    alert_status = fields.Selection([
        ('active', 'Active'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('ignored', 'Ignored')
    ], default='active', tracking=True)

    acknowledged_by = fields.Many2one(
        'res.users',
        string='Acknowledged By'
    )

    acknowledged_date = fields.Datetime(
        string='Acknowledged Date'
    )

    resolution_notes = fields.Text('Resolution Notes')

    @api.depends('location_id')
    def _compute_warehouse_id(self):
        for alert in self:
            warehouse = self.env['stock.warehouse'].search([
                ('lot_stock_id', 'parent_of', alert.location_id.id)
            ], limit=1)
            alert.warehouse_id = warehouse.id if warehouse else False

    @api.depends('product_id', 'lot_id', 'location_id')
    def _compute_current_qty(self):
        for alert in self:
            if alert.product_id and alert.lot_id and alert.location_id:
                quants = self.env['stock.quant'].search([
                    ('product_id', '=', alert.product_id.id),
                    ('lot_id', '=', alert.lot_id.id),
                    ('location_id', '=', alert.location_id.id)
                ])
                alert.current_qty = sum(quants.mapped('quantity'))
            else:
                alert.current_qty = 0.0

    @api.depends('expiry_date')
    def _compute_days_to_expiry(self):
        today = fields.Date.today()
        for alert in self:
            if alert.expiry_date:
                delta = alert.expiry_date - today
                alert.days_to_expiry = delta.days
            else:
                alert.days_to_expiry = 0

    @api.depends('days_to_expiry')
    def _compute_urgency_level(self):
        for alert in self:
            days = alert.days_to_expiry
            if days < 0:
                alert.urgency_level = 'expired'
            elif days <= 7:
                alert.urgency_level = 'critical'
            elif days <= 30:
                alert.urgency_level = 'warning'
            else:
                alert.urgency_level = 'normal'

    def action_acknowledge(self):
        """Acknowledge the alert"""
        self.write({
            'alert_status': 'acknowledged',
            'acknowledged_by': self.env.user.id,
            'acknowledged_date': fields.Datetime.now()
        })

    def action_resolve(self):
        """Mark alert as resolved"""
        self.alert_status = 'resolved'

    def action_ignore(self):
        """Ignore the alert"""
        self.alert_status = 'ignored'

    @api.model
    def generate_expiry_alerts(self):
        """Cron job to generate expiry alerts"""
        # Clear existing active alerts
        self.search([('alert_status', '=', 'active')]).unlink()

        # Get alert threshold from configuration
        alert_days = int(self.env['ir.config_parameter'].sudo().get_param(
            'vetlane.expiry.alert.days', 30
        ))

        # Calculate cutoff date
        cutoff_date = fields.Date.today() + timedelta(days=alert_days)

        # Find lots expiring within threshold
        expiring_lots = self.env['stock.lot'].search([
            ('expiration_date', '<=', cutoff_date),
            ('expiration_date', '!=', False)
        ])

        alerts_created = 0
        for lot in expiring_lots:
            # Find stock quants for this lot
            quants = self.env['stock.quant'].search([
                ('lot_id', '=', lot.id),
                ('quantity', '>', 0),
                ('location_id.usage', '=', 'internal')
            ])

            for quant in quants:
                # Check if alert already exists
                existing_alert = self.search([
                    ('product_id', '=', quant.product_id.id),
                    ('lot_id', '=', lot.id),
                    ('location_id', '=', quant.location_id.id),
                    ('alert_status', 'in', ['active', 'acknowledged'])
                ])

                if not existing_alert:
                    self.create({
                        'product_id': quant.product_id.id,
                        'lot_id': lot.id,
                        'location_id': quant.location_id.id
                    })
                    alerts_created += 1

        # Log the result
        _logger.info(f"Expiry alert cron job completed. Created {alerts_created} new alerts.")

        return alerts_created
```

##### **2. Expiry Dashboard Model**
```python
class ExpiryDashboard(models.Model):
    _name = 'expiry.dashboard'
    _description = 'Expiry Dashboard'

    @api.model
    def get_dashboard_data(self, branch_id=None):
        """Get dashboard data for expiry alerts"""
        domain = [('alert_status', '=', 'active')]
        if branch_id:
            domain.append(('branch_id', '=', branch_id))

        alerts = self.env['inventory.expiry.alert'].search(domain)

        # Group by urgency level
        urgency_counts = {
            'expired': 0,
            'critical': 0,
            'warning': 0,
            'normal': 0
        }

        total_value_at_risk = 0.0

        for alert in alerts:
            urgency_counts[alert.urgency_level] += 1
            # Calculate value at risk
            product_cost = alert.product_id.standard_price or 0
            value_at_risk = alert.current_qty * product_cost
            total_value_at_risk += value_at_risk

        # Get top 10 most urgent alerts
        top_alerts = alerts.sorted(lambda a: (a.urgency_level == 'expired',
                                            a.urgency_level == 'critical',
                                            a.days_to_expiry))[:10]

        return {
            'urgency_counts': urgency_counts,
            'total_alerts': len(alerts),
            'total_value_at_risk': total_value_at_risk,
            'top_alerts': [{
                'id': alert.id,
                'product_name': alert.product_id.name,
                'lot_name': alert.lot_id.name,
                'expiry_date': alert.expiry_date,
                'days_to_expiry': alert.days_to_expiry,
                'urgency_level': alert.urgency_level,
                'current_qty': alert.current_qty,
                'location': alert.location_id.complete_name,
                'branch': alert.branch_id.name
            } for alert in top_alerts]
        }
```

#### **UI/UX Implementation**
```xml
<!-- Expiry Alert Dashboard View -->
<record id="view_expiry_dashboard" model="ir.ui.view">
    <field name="name">expiry.dashboard</field>
    <field name="model">expiry.dashboard</field>
    <field name="arch" type="xml">
        <form string="Expiry Dashboard" create="false" edit="false" delete="false">
            <div class="o_expiry_dashboard">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h4 class="card-title">Expired</h4>
                                <h2 id="expired_count">0</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h4 class="card-title">Critical (≤7 days)</h4>
                                <h2 id="critical_count">0</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h4 class="card-title">Warning (≤30 days)</h4>
                                <h2 id="warning_count">0</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h4 class="card-title">Total Value at Risk</h4>
                                <h2 id="value_at_risk">₦0</h2>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Most Urgent Alerts</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-striped" id="urgent_alerts_table">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th>Lot</th>
                                            <th>Expiry Date</th>
                                            <th>Days to Expiry</th>
                                            <th>Quantity</th>
                                            <th>Location</th>
                                            <th>Branch</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </field>
</record>

<!-- Expiry Alert List View -->
<record id="view_inventory_expiry_alert_tree" model="ir.ui.view">
    <field name="name">inventory.expiry.alert.tree</field>
    <field name="model">inventory.expiry.alert</field>
    <field name="arch" type="xml">
        <tree decoration-danger="urgency_level=='expired'"
              decoration-warning="urgency_level=='critical'"
              decoration-info="urgency_level=='warning'">
            <field name="product_id"/>
            <field name="lot_id"/>
            <field name="current_qty"/>
            <field name="expiry_date"/>
            <field name="days_to_expiry"/>
            <field name="urgency_level"/>
            <field name="location_id"/>
            <field name="branch_id"/>
            <field name="alert_status"/>
            <button name="action_acknowledge" type="object" string="Acknowledge"
                    attrs="{'invisible': [('alert_status', '!=', 'active')]}"/>
        </tree>
    </field>
</record>
```

#### **Cron Job Configuration**
```xml
<!-- Expiry Alert Cron Job -->
<record id="cron_generate_expiry_alerts" model="ir.cron">
    <field name="name">Generate Expiry Alerts</field>
    <field name="model_id" ref="model_inventory_expiry_alert"/>
    <field name="state">code</field>
    <field name="code">model.generate_expiry_alerts()</field>
    <field name="interval_number">1</field>
    <field name="interval_type">days</field>
    <field name="numbercall">-1</field>
    <field name="active">True</field>
    <field name="doall">False</field>
</record>
```

#### **Testing Strategy**
- Unit tests for expiry alert generation
- Dashboard data calculation tests
- Cron job execution tests
- Urgency level computation tests

#### **Estimated Effort:** Medium (3-4 weeks)
#### **Acceptance Criteria:**
- ✅ Daily cron job generates expiry alerts
- ✅ Color-coded urgency system works correctly
- ✅ Dashboard displays real-time expiry data
- ✅ Alerts can be acknowledged and resolved
- ✅ Value at risk calculations are accurate

---

## **DOCUMENT STRUCTURE OVERVIEW**

This document will contain detailed specifications for all 25 modules organized as follows:

**PART 1: CORE INFRASTRUCTURE MODULES (2)**
- MODULE 1: vetlane_base - Foundation Module ✓
- MODULE 2: vetlane_multi_branch - Multi-Branch Infrastructure [IN PROGRESS]

**PART 2: POS ENHANCEMENT MODULES (5)**
- MODULE 3: vetlane_pos_supervisor_approval
- MODULE 4: vetlane_pos_multi_branch_stock  
- MODULE 5: vetlane_pos_returns_exchange
- MODULE 6: vetlane_pos_cash_management
- MODULE 7: vetlane_pos_user_management

**PART 3: INVENTORY & PROCUREMENT MODULES (4)**
- MODULE 8: vetlane_inventory_lot_tracking
- MODULE 9: vetlane_inventory_expiry_alerts
- MODULE 10: vetlane_purchase_enhanced
- MODULE 11: vetlane_procurement_grn

**PART 4: PRODUCT MANAGEMENT MODULE (1)**
- MODULE 12: vetlane_product_attribute_variant

**PART 5: BUSINESS PROCESS MODULES (3)**
- MODULE 13: vetlane_grooming_management
- MODULE 14: vetlane_delivery_management
- MODULE 15: vetlane_crm_enhanced

**PART 6: E-COMMERCE ENHANCEMENT MODULES (5)**
- MODULE 16: vetlane_website_branch_stock
- MODULE 17: vetlane_website_enhanced_checkout
- MODULE 18: vetlane_website_product_variants
- MODULE 19: vetlane_website_whatsapp_chat
- MODULE 20: vetlane_website_theme_custom

**PART 7: BACKEND & ADMINISTRATIVE MODULES (4)**
- MODULE 21: vetlane_accounting_enhanced
- MODULE 22: vetlane_marketing_automation
- MODULE 23: vetlane_inventory_inter_branch
- MODULE 24: vetlane_shipping_calculator

**PART 8: SUPPORT & TOOLS MODULE (1)**
- MODULE 25: vetlane_support_tools

Each module section will include:
- Module Overview & Dependencies
- Key Models & Fields (Complete Python code)
- UI/UX Implementation (XML views, QWeb templates)
- JavaScript Extensions (where applicable)
- Business Logic & Technical Considerations
- Testing Strategy & Unit Tests
- Estimated Effort & Acceptance Criteria
