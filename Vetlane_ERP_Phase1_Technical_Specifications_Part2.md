# **VETLANE ERP PHASE 1 - TECHNICAL SPECIFICATIONS PART 2**

**Project:** Vetlane ERP v4.0  
**Phase:** 1 (Core MVP)  
**Date:** December 2024  
**Author:** Zoe - 100x Engineer  
**Status:** In Development  
**Document:** Part 2 of 3

## **DOCUMENT OVERVIEW**

This is Part 2 of the comprehensive technical specifications for Vetlane ERP Phase 1. 

**Part 1** (Completed): Core Infrastructure + POS Enhancement + First 2 Inventory Modules (9 modules)
**Part 2** (This Document): Remaining Inventory & Procurement + Product Management + Business Processes (7 modules)
**Part 3** (Next Document): E-commerce + Backend & Admin + Support Tools (9 modules)

---

## **PART 3: INVENTORY & PROCUREMENT MODULES (CONTINUED)**

### **MODULE 10: `vetlane_purchase_enhanced` - Purchase Module Enhancements**

#### **Module Overview**
**Purpose:** Implements simplified UI, admin editing controls, and automatic price updates (FR-PUR1, FR-PUR2, FR-PUR3, FR-PUR4)

**Dependencies:** `vetlane_base`, `purchase`, `stock`

#### **Key Models & Fields**

##### **1. Extended Purchase Order**
```python
class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'
    _inherit = ['purchase.order', 'vetlane.audit.mixin']
    
    admin_edit_mode = fields.Boolean(
        string='Admin Edit Mode',
        default=False,
        help='Allows admin to edit confirmed purchase orders'
    )
    
    reversed_order_id = fields.Many2one(
        'purchase.order',
        string='Reversed Order',
        help='Reference to the order that reverses this one'
    )
    
    original_order_id = fields.Many2one(
        'purchase.order',
        string='Original Order',
        help='Reference to the original order (for reversed orders)'
    )
    
    is_reversal = fields.Boolean(
        string='Is Reversal Order',
        default=False,
        help='Indicates this is a reversal order'
    )
    
    reversal_reason = fields.Text('Reversal Reason')
    
    simplified_view = fields.Boolean(
        string='Simplified View',
        default=True,
        help='Show simplified interface for non-admin users'
    )
    
    def action_enable_admin_edit(self):
        """Enable admin edit mode"""
        if not self.env.user.has_group('vetlane_base.group_vetlane_admin'):
            raise UserError("Only administrators can enable edit mode")
        
        self.admin_edit_mode = True
        self.message_post(body=f"Admin edit mode enabled by {self.env.user.name}")
    
    def action_disable_admin_edit(self):
        """Disable admin edit mode"""
        self.admin_edit_mode = False
        self.message_post(body=f"Admin edit mode disabled by {self.env.user.name}")
    
    def action_reverse_order(self):
        """Create a reversal order"""
        if not self.env.user.has_group('vetlane_base.group_vetlane_admin'):
            raise UserError("Only administrators can reverse orders")
        
        if self.state not in ['purchase', 'done']:
            raise UserError("Only confirmed orders can be reversed")
        
        # Create reversal order
        reversal_vals = {
            'partner_id': self.partner_id.id,
            'currency_id': self.currency_id.id,
            'company_id': self.company_id.id,
            'origin': f"Reversal of {self.name}",
            'is_reversal': True,
            'original_order_id': self.id,
            'order_line': [(0, 0, {
                'product_id': line.product_id.id,
                'product_qty': -line.product_qty,  # Negative quantities
                'price_unit': line.price_unit,
                'name': f"Reversal: {line.name}",
                'date_planned': fields.Datetime.now(),
            }) for line in self.order_line]
        }
        
        reversal_order = self.create(reversal_vals)
        
        # Link orders
        self.reversed_order_id = reversal_order.id
        
        # Auto-confirm reversal
        reversal_order.button_confirm()
        
        self.message_post(
            body=f"Order reversed. Reversal order: {reversal_order.name}"
        )
        
        return {
            'type': 'ir.actions.act_window',
            'name': 'Reversal Order',
            'res_model': 'purchase.order',
            'res_id': reversal_order.id,
            'view_mode': 'form',
            'target': 'current'
        }
    
    def action_recreate_order(self):
        """Recreate order from original or reversed order"""
        if not self.env.user.has_group('vetlane_base.group_vetlane_admin'):
            raise UserError("Only administrators can recreate orders")
        
        # Use original order if this is a reversal, otherwise use current order
        source_order = self.original_order_id if self.is_reversal else self
        
        recreated_vals = {
            'partner_id': source_order.partner_id.id,
            'currency_id': source_order.currency_id.id,
            'company_id': source_order.company_id.id,
            'origin': f"Recreated from {source_order.name}",
            'order_line': [(0, 0, {
                'product_id': line.product_id.id,
                'product_qty': abs(line.product_qty),  # Use absolute values
                'price_unit': line.price_unit,
                'name': line.name,
                'date_planned': fields.Datetime.now() + timedelta(days=7),
            }) for line in source_order.order_line]
        }
        
        recreated_order = self.create(recreated_vals)
        
        return {
            'type': 'ir.actions.act_window',
            'name': 'Recreated Order',
            'res_model': 'purchase.order',
            'res_id': recreated_order.id,
            'view_mode': 'form',
            'target': 'current'
        }
    
    def write(self, vals):
        """Override write to log changes when in admin edit mode"""
        if self.admin_edit_mode and self.state in ['purchase', 'done']:
            # Log all changes
            for record in self:
                for field, new_value in vals.items():
                    if field in record._get_auditable_fields():
                        old_value = getattr(record, field)
                        if old_value != new_value:
                            record.message_post(
                                body=f"Admin Edit: {field} changed from {old_value} to {new_value} by {self.env.user.name}"
                            )
        
        return super().write(vals)
    
    def _get_auditable_fields(self):
        """Define fields to audit"""
        return ['partner_id', 'currency_id', 'payment_term_id', 'order_line']

class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'
    _inherit = ['purchase.order.line', 'vetlane.audit.mixin']
    
    sale_price = fields.Float(
        string='Sale Price',
        help='New sale price to update on product'
    )
    
    update_sale_price = fields.Boolean(
        string='Update Sale Price',
        default=False,
        help='Update product sale price when PO is confirmed'
    )
    
    cost_price_updated = fields.Boolean(
        string='Cost Price Updated',
        default=False,
        help='Indicates if cost price was updated from this line'
    )
    
    @api.onchange('price_unit')
    def _onchange_price_unit(self):
        """Auto-update cost price when purchase price changes"""
        if self.product_id and self.price_unit:
            # Update product standard price (cost price)
            if self.price_unit != self.product_id.standard_price:
                self.product_id.standard_price = self.price_unit
                self.cost_price_updated = True
    
    @api.model
    def create(self, vals):
        """Set default sale price from product"""
        if 'product_id' in vals and not vals.get('sale_price'):
            product = self.env['product.product'].browse(vals['product_id'])
            vals['sale_price'] = product.list_price
        return super().create(vals)
    
    def _update_product_prices(self):
        """Update product prices when PO is confirmed"""
        for line in self:
            if line.product_id:
                # Update cost price
                if line.price_unit != line.product_id.standard_price:
                    line.product_id.standard_price = line.price_unit
                
                # Update sale price if requested
                if line.update_sale_price and line.sale_price:
                    line.product_id.list_price = line.sale_price
                    
                    line.order_id.message_post(
                        body=f"Sale price updated for {line.product_id.name}: "
                             f"₦{line.product_id.list_price:,.2f}"
                    )
    
    def _get_auditable_fields(self):
        """Define fields to audit"""
        return ['product_qty', 'price_unit', 'sale_price']
```

##### **2. Purchase Order Confirmation Override**
```python
class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'
    
    def button_confirm(self):
        """Override to update product prices and show confirmation dialog"""
        # Check if any lines have sale price updates
        lines_with_price_updates = self.order_line.filtered('update_sale_price')
        
        if lines_with_price_updates:
            # Show confirmation dialog
            return {
                'type': 'ir.actions.act_window',
                'name': 'Confirm Price Updates',
                'res_model': 'purchase.price.update.wizard',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_purchase_order_id': self.id,
                    'price_updates': [(line.product_id.name, line.sale_price) 
                                    for line in lines_with_price_updates]
                }
            }
        else:
            # Standard confirmation
            result = super().button_confirm()
            
            # Update product prices
            for line in self.order_line:
                line._update_product_prices()
            
            return result

class PurchasePriceUpdateWizard(models.TransientModel):
    _name = 'purchase.price.update.wizard'
    _description = 'Purchase Price Update Confirmation'
    
    purchase_order_id = fields.Many2one(
        'purchase.order',
        string='Purchase Order',
        required=True
    )
    
    price_update_line_ids = fields.One2many(
        'purchase.price.update.wizard.line',
        'wizard_id',
        string='Price Updates'
    )
    
    @api.model
    def default_get(self, fields_list):
        """Populate price update lines"""
        result = super().default_get(fields_list)
        
        if 'purchase_order_id' in result:
            po = self.env['purchase.order'].browse(result['purchase_order_id'])
            lines = []
            
            for line in po.order_line.filtered('update_sale_price'):
                lines.append((0, 0, {
                    'product_id': line.product_id.id,
                    'current_price': line.product_id.list_price,
                    'new_price': line.sale_price,
                    'po_line_id': line.id
                }))
            
            result['price_update_line_ids'] = lines
        
        return result
    
    def action_confirm_updates(self):
        """Confirm price updates and PO"""
        # Update prices
        for line in self.price_update_line_ids:
            if line.confirm_update:
                line.po_line_id._update_product_prices()
        
        # Confirm purchase order
        self.purchase_order_id.button_confirm()
        
        return {'type': 'ir.actions.act_window_close'}

class PurchasePriceUpdateWizardLine(models.TransientModel):
    _name = 'purchase.price.update.wizard.line'
    _description = 'Purchase Price Update Line'
    
    wizard_id = fields.Many2one(
        'purchase.price.update.wizard',
        required=True,
        ondelete='cascade'
    )
    
    product_id = fields.Many2one(
        'product.product',
        string='Product',
        required=True
    )
    
    po_line_id = fields.Many2one(
        'purchase.order.line',
        string='PO Line',
        required=True
    )
    
    current_price = fields.Float(
        string='Current Sale Price',
        readonly=True
    )
    
    new_price = fields.Float(
        string='New Sale Price',
        readonly=True
    )
    
    price_difference = fields.Float(
        string='Price Difference',
        compute='_compute_price_difference'
    )
    
    confirm_update = fields.Boolean(
        string='Confirm Update',
        default=True
    )
    
    @api.depends('current_price', 'new_price')
    def _compute_price_difference(self):
        for line in self:
            line.price_difference = line.new_price - line.current_price
```

#### **UI/UX Implementation**

##### **Simplified Purchase Order View**
```xml
<!-- Simplified Purchase Order Form for Non-Admins -->
<record id="view_purchase_order_form_simplified" model="ir.ui.view">
    <field name="name">purchase.order.form.simplified</field>
    <field name="model">purchase.order</field>
    <field name="inherit_id" ref="purchase.purchase_order_form"/>
    <field name="arch" type="xml">
        <!-- Hide complex fields for non-admin users -->
        <xpath expr="//field[@name='payment_term_id']" position="attributes">
            <attribute name="attrs">{'invisible': [('simplified_view', '=', True)]}</attribute>
        </xpath>
        <xpath expr="//field[@name='fiscal_position_id']" position="attributes">
            <attribute name="attrs">{'invisible': [('simplified_view', '=', True)]}</attribute>
        </xpath>
        
        <!-- Add admin controls -->
        <xpath expr="//header" position="inside">
            <button name="action_enable_admin_edit" type="object" string="Enable Admin Edit" 
                    attrs="{'invisible': ['|', ('admin_edit_mode', '=', True), ('state', 'not in', ['purchase', 'done'])]}"
                    groups="vetlane_base.group_vetlane_admin" class="btn-warning"/>
            <button name="action_disable_admin_edit" type="object" string="Disable Admin Edit" 
                    attrs="{'invisible': [('admin_edit_mode', '=', False)]}"
                    groups="vetlane_base.group_vetlane_admin" class="btn-secondary"/>
            <button name="action_reverse_order" type="object" string="Reverse PO" 
                    attrs="{'invisible': [('state', 'not in', ['purchase', 'done'])]}"
                    groups="vetlane_base.group_vetlane_admin" class="btn-danger"/>
            <button name="action_recreate_order" type="object" string="Recreate PO" 
                    groups="vetlane_base.group_vetlane_admin" class="btn-info"/>
        </xpath>
        
        <!-- Add sale price fields to order lines -->
        <xpath expr="//field[@name='order_line']/tree/field[@name='price_unit']" position="after">
            <field name="sale_price"/>
            <field name="update_sale_price"/>
        </xpath>
    </field>
</record>

<!-- Price Update Wizard -->
<record id="view_purchase_price_update_wizard_form" model="ir.ui.view">
    <field name="name">purchase.price.update.wizard.form</field>
    <field name="model">purchase.price.update.wizard</field>
    <field name="arch" type="xml">
        <form string="Confirm Price Updates">
            <p>The following products will have their sale prices updated:</p>
            <field name="price_update_line_ids">
                <tree editable="bottom">
                    <field name="product_id" readonly="1"/>
                    <field name="current_price" readonly="1"/>
                    <field name="new_price" readonly="1"/>
                    <field name="price_difference" readonly="1"/>
                    <field name="confirm_update"/>
                </tree>
            </field>
            <footer>
                <button string="Confirm Updates" name="action_confirm_updates" type="object" class="btn-primary"/>
                <button string="Cancel" class="btn-secondary" special="cancel"/>
            </footer>
        </form>
    </field>
</record>
```

#### **Testing Strategy**
- Unit tests for admin edit mode functionality
- Price update confirmation workflow tests
- Order reversal and recreation tests
- Simplified view display tests

#### **Estimated Effort:** Large (4-5 weeks)
#### **Acceptance Criteria:**
- ✅ Simplified UI for non-admin users
- ✅ Admin can edit confirmed orders with audit trail
- ✅ Order reversal creates negative quantities
- ✅ Order recreation wizard works correctly
- ✅ Automatic cost and sale price updates
- ✅ Price update confirmation dialog functions

---

### **MODULE 11: `vetlane_procurement_grn` - GRN & Quality Control**

#### **Module Overview**
**Purpose:** Implements formalized GRN workflow with quality control and inter-branch transfers (FR-PROC1, FR-PROC2)

**Dependencies:** `vetlane_base`, `stock`, `purchase`

#### **Key Models & Fields**

##### **1. Goods Receipt Notice (GRN) Model**
```python
class GoodsReceiptNotice(models.Model):
    _name = 'goods.receipt.notice'
    _description = 'Goods Receipt Notice'
    _inherit = ['mail.thread', 'vetlane.audit.mixin']
    _order = 'create_date desc'

    name = fields.Char(
        string='GRN Reference',
        default=lambda self: VetlaneUtils.generate_reference_number('GRN'),
        required=True
    )

    purchase_order_id = fields.Many2one(
        'purchase.order',
        string='Purchase Order',
        required=True
    )

    picking_id = fields.Many2one(
        'stock.picking',
        string='Stock Picking',
        required=True
    )

    supplier_id = fields.Many2one(
        'res.partner',
        string='Supplier',
        related='purchase_order_id.partner_id',
        readonly=True
    )

    warehouse_id = fields.Many2one(
        'stock.warehouse',
        string='Warehouse',
        required=True
    )

    branch_id = fields.Many2one(
        'res.company',
        string='Branch',
        related='warehouse_id.branch_id',
        readonly=True
    )

    state = fields.Selection([
        ('draft', 'Draft'),
        ('qc_in_progress', 'QC In Progress'),
        ('qc_completed', 'QC Completed'),
        ('received', 'Received'),
        ('cancelled', 'Cancelled')
    ], default='draft', tracking=True)

    grn_line_ids = fields.One2many(
        'goods.receipt.notice.line',
        'grn_id',
        string='GRN Lines'
    )

    qc_officer_id = fields.Many2one(
        'res.users',
        string='QC Officer',
        domain="[('groups_id', 'in', %(stock.group_stock_user)d)]"
    )

    warehouse_manager_id = fields.Many2one(
        'res.users',
        string='Warehouse Manager',
        domain="[('groups_id', 'in', %(stock.group_stock_manager)d)]"
    )

    qc_start_date = fields.Datetime('QC Start Date')
    qc_completion_date = fields.Datetime('QC Completion Date')
    receipt_date = fields.Datetime('Receipt Date')

    total_expected_qty = fields.Float(
        string='Total Expected Quantity',
        compute='_compute_totals'
    )

    total_received_qty = fields.Float(
        string='Total Received Quantity',
        compute='_compute_totals'
    )

    total_on_hold_qty = fields.Float(
        string='Total On Hold Quantity',
        compute='_compute_totals'
    )

    qc_pass_rate = fields.Float(
        string='QC Pass Rate (%)',
        compute='_compute_qc_pass_rate'
    )

    @api.depends('grn_line_ids.expected_qty', 'grn_line_ids.received_qty', 'grn_line_ids.on_hold_qty')
    def _compute_totals(self):
        for grn in self:
            grn.total_expected_qty = sum(grn.grn_line_ids.mapped('expected_qty'))
            grn.total_received_qty = sum(grn.grn_line_ids.mapped('received_qty'))
            grn.total_on_hold_qty = sum(grn.grn_line_ids.mapped('on_hold_qty'))

    @api.depends('total_expected_qty', 'total_received_qty')
    def _compute_qc_pass_rate(self):
        for grn in self:
            if grn.total_expected_qty > 0:
                grn.qc_pass_rate = (grn.total_received_qty / grn.total_expected_qty) * 100
            else:
                grn.qc_pass_rate = 0.0

    @api.model
    def create_from_purchase_order(self, purchase_order_id):
        """Create GRN from purchase order"""
        po = self.env['purchase.order'].browse(purchase_order_id)

        if not po.picking_ids:
            raise UserError("No stock picking found for this purchase order")

        picking = po.picking_ids[0]  # Use first picking

        grn_vals = {
            'purchase_order_id': po.id,
            'picking_id': picking.id,
            'warehouse_id': picking.location_dest_id.warehouse_id.id,
        }

        grn = self.create(grn_vals)

        # Create GRN lines from PO lines
        for po_line in po.order_line:
            self.env['goods.receipt.notice.line'].create({
                'grn_id': grn.id,
                'product_id': po_line.product_id.id,
                'expected_qty': po_line.product_qty,
                'po_line_id': po_line.id
            })

        return grn

    def action_start_qc(self):
        """Start quality control process"""
        if not self.env.user.has_group('stock.group_stock_user'):
            raise UserError("Only QC officers can start quality control")

        self.write({
            'state': 'qc_in_progress',
            'qc_officer_id': self.env.user.id,
            'qc_start_date': fields.Datetime.now()
        })

    def action_complete_qc(self):
        """Complete quality control"""
        # Check if all lines have been QC checked
        unchecked_lines = self.grn_line_ids.filtered(lambda l: not l.qc_completed)
        if unchecked_lines:
            raise UserError("All items must complete QC before finalizing")

        self.write({
            'state': 'qc_completed',
            'qc_completion_date': fields.Datetime.now()
        })

    def action_finalize_receipt(self):
        """Finalize the receipt"""
        if not self.env.user.has_group('stock.group_stock_manager'):
            raise UserError("Only warehouse managers can finalize receipts")

        if self.state != 'qc_completed':
            raise UserError("QC must be completed before finalizing receipt")

        # Process stock moves for passed items
        for line in self.grn_line_ids.filtered(lambda l: l.qc_status == 'pass'):
            line._process_stock_move()

        # Move failed items to quarantine
        for line in self.grn_line_ids.filtered(lambda l: l.qc_status == 'fail'):
            line._move_to_quarantine()

        self.write({
            'state': 'received',
            'warehouse_manager_id': self.env.user.id,
            'receipt_date': fields.Datetime.now()
        })

        # Complete the stock picking
        if self.picking_id.state != 'done':
            self.picking_id.action_done()

class GoodsReceiptNoticeLine(models.Model):
    _name = 'goods.receipt.notice.line'
    _description = 'Goods Receipt Notice Line'

    grn_id = fields.Many2one(
        'goods.receipt.notice',
        string='GRN',
        required=True,
        ondelete='cascade'
    )

    product_id = fields.Many2one(
        'product.product',
        string='Product',
        required=True
    )

    po_line_id = fields.Many2one(
        'purchase.order.line',
        string='PO Line'
    )

    expected_qty = fields.Float(
        string='Expected Quantity',
        required=True
    )

    scanned_qty = fields.Float(
        string='Scanned Quantity',
        default=0.0
    )

    received_qty = fields.Float(
        string='Received Quantity',
        default=0.0
    )

    on_hold_qty = fields.Float(
        string='On Hold Quantity',
        default=0.0
    )

    qc_status = fields.Selection([
        ('pending', 'Pending'),
        ('pass', 'Pass'),
        ('fail', 'Fail')
    ], default='pending', string='QC Status')

    qc_completed = fields.Boolean(
        string='QC Completed',
        default=False
    )

    qc_checklist_ids = fields.One2many(
        'grn.qc.checklist',
        'grn_line_id',
        string='QC Checklist'
    )

    qc_notes = fields.Text('QC Notes')

    failure_reason = fields.Text('Failure Reason')

    lot_serial_number = fields.Char('Lot/Serial Number')

    def action_scan_item(self, barcode):
        """Scan item barcode"""
        # Verify barcode matches product
        if self.product_id.barcode != barcode:
            raise UserError(f"Scanned barcode {barcode} does not match expected product")

        self.scanned_qty += 1

        # Auto-create QC checklist if not exists
        if not self.qc_checklist_ids:
            self._create_qc_checklist()

    def _create_qc_checklist(self):
        """Create QC checklist items"""
        checklist_items = [
            ('packaging_integrity', 'Packaging Integrity'),
            ('weight_verified', 'Weight Verified'),
            ('expiry_date_check', 'Expiry Date Check'),
            ('visual_inspection', 'Visual Inspection'),
            ('quantity_verification', 'Quantity Verification')
        ]

        for code, name in checklist_items:
            self.env['grn.qc.checklist'].create({
                'grn_line_id': self.id,
                'check_code': code,
                'check_name': name,
                'status': 'pending'
            })

    def action_complete_qc(self):
        """Complete QC for this line"""
        # Check if all checklist items are completed
        pending_checks = self.qc_checklist_ids.filtered(lambda c: c.status == 'pending')
        if pending_checks:
            raise UserError("All QC checks must be completed")

        # Determine overall QC status
        failed_checks = self.qc_checklist_ids.filtered(lambda c: c.status == 'fail')
        if failed_checks:
            self.qc_status = 'fail'
            self.on_hold_qty = self.scanned_qty
            self.received_qty = 0

            # Notify warehouse manager
            self.grn_id.message_post(
                body=f"QC Failed for {self.product_id.name}. Reason: {self.failure_reason}",
                partner_ids=[self.grn_id.warehouse_manager_id.partner_id.id]
            )
        else:
            self.qc_status = 'pass'
            self.received_qty = self.scanned_qty
            self.on_hold_qty = 0

        self.qc_completed = True

    def _process_stock_move(self):
        """Process stock move for passed items"""
        # Find corresponding stock move
        move = self.grn_id.picking_id.move_lines.filtered(
            lambda m: m.product_id == self.product_id
        )

        if move:
            # Update move quantity
            move.quantity_done = self.received_qty

    def _move_to_quarantine(self):
        """Move failed items to quarantine location"""
        quarantine_location = self.env['stock.location'].search([
            ('location_type', '=', 'quarantine'),
            ('warehouse_id', '=', self.grn_id.warehouse_id.id)
        ], limit=1)

        if not quarantine_location:
            # Create quarantine location if not exists
            quarantine_location = self.env['stock.location'].create({
                'name': f"{self.grn_id.warehouse_id.name} - Quarantine",
                'location_id': self.grn_id.warehouse_id.lot_stock_id.id,
                'usage': 'internal',
                'location_type': 'quarantine',
                'warehouse_id': self.grn_id.warehouse_id.id
            })

        # Create stock move to quarantine
        self.env['stock.move'].create({
            'name': f"Quarantine: {self.product_id.name}",
            'product_id': self.product_id.id,
            'product_uom_qty': self.on_hold_qty,
            'product_uom': self.product_id.uom_id.id,
            'location_id': self.grn_id.picking_id.location_dest_id.id,
            'location_dest_id': quarantine_location.id,
            'picking_id': self.grn_id.picking_id.id
        })

class GrnQcChecklist(models.Model):
    _name = 'grn.qc.checklist'
    _description = 'GRN QC Checklist Item'

    grn_line_id = fields.Many2one(
        'goods.receipt.notice.line',
        string='GRN Line',
        required=True,
        ondelete='cascade'
    )

    check_code = fields.Char(
        string='Check Code',
        required=True
    )

    check_name = fields.Char(
        string='Check Name',
        required=True
    )

    status = fields.Selection([
        ('pending', 'Pending'),
        ('pass', 'Pass'),
        ('fail', 'Fail')
    ], default='pending', required=True)

    notes = fields.Text('Notes')

    checked_by = fields.Many2one(
        'res.users',
        string='Checked By'
    )

    check_date = fields.Datetime('Check Date')

    def action_mark_pass(self):
        """Mark check as passed"""
        self.write({
            'status': 'pass',
            'checked_by': self.env.user.id,
            'check_date': fields.Datetime.now()
        })

    def action_mark_fail(self):
        """Mark check as failed"""
        self.write({
            'status': 'fail',
            'checked_by': self.env.user.id,
            'check_date': fields.Datetime.now()
        })
```

#### **Testing Strategy**
- Unit tests for GRN creation from purchase orders
- QC workflow and checklist tests
- Stock movement and quarantine tests
- Barcode scanning functionality tests

#### **Estimated Effort:** Large (5-6 weeks)
#### **Acceptance Criteria:**
- ✅ GRN created from confirmed purchase orders
- ✅ QC checklist workflow functions properly
- ✅ Barcode scanning validates products
- ✅ Failed items moved to quarantine
- ✅ Warehouse manager approval required
- ✅ Stock moves processed correctly
