Vetlane ERP: Functional Step by Step Product Requirements Document 
● Version: 4.0 
● Date: June 6, 2025 
● Status: In Progress 
● Author: VETLANE ANIMAL HEALTHCARE  
The Definitive PRD - Master Table of Contents 
Part 1: The Strategic Foundation 
● 1.1. Executive Summary & Problem Statement 
● 1.2. Business Objectives & Measurable Success Metrics 
● 1.3. Scope & Phased Implementation 
● 1.4. User Personas & Roles 
Part 2: Point of Sale (POS) Functional Requirements 
● FR-POS1: Conditional Supervisor Approval Workflow 
● FR-POS2: Cashier-Only Sale Finalization 
● FR-POS3: Integrated Returns & Exchanges Workflow 
● FR-POS4: Role-Based Cost Price Suppression 
● FR-POS5: Multi-Branch Stock Display & Fulfillment Selection 
● FR-POS6: Bulk User Import via CSV 
● FR-POS7: Live Customer Search & Inline Creation 
● FR-POS8: Post-Sale Transaction Lock 
● FR-POS9: "Cash with Cashier" Custom Payment Method 
● FR-POS10: End-of-Day Cash Deposit & Reconciliation Wizard 
● FR-POS11: Supervisor In-Cart Item Deletion 
● FR-POS12: Dynamic Delivery Fee Editing in POS 
Part 3: Inventory, Purchasing & Procurement Functional Requirements 
● FR-INV1: Multi-Branch Warehouse & Internal Location Configuration 
● FR-INV2: Mandatory Lot, Manufacturing & Expiry Date on PO Lines 
● FR-INV3: Multi-Lot Entry per PO Line 
● FR-INV4: Expiry-Alert Dashboard & Cron Job 
● FR-INV5: Inter-Branch Transfer with Receipt Confirmation 
● FR-PUR1: Simplified Purchase Module UI/UX 
● FR-PUR2: Admin Purchase Order Editing, Reversal & Audit Trail 
● FR-PUR3: Automatic Sales Price Update from PO Line 
● FR-PUR4: Automatic Cost Price Update from PO Line 
● FR-PROC1: Formalized Goods Receipt Notice (GRN) & QC Workflow 
● FR-PROC2: Streamlined Request for Quotation (RFQ) Workflow 
● FR-PROD1: Centralized Product Attribute & Variant Configuration 
Part 4: Business Process Functional Requirements (Grooming, Delivery, CRM) 
● Grooming: 
○ FR-GRT1: Grooming Service & Booking Data Model 
○ FR-GRT2: Grooming Availability Slot Configuration 
○ FR-GRT3: Pet & Owner Record Lookup from Booking UI 
○ FR-GRT4: Excel-Style Custom Grooming Report View 
● Delivery: 
○ FR-DEL1: Zone-Based Delivery Fee Structure 
○ FR-DEL2: Dynamic Delivery Fee Calculation (POS & Website) 
○ FR-DEL3: Spreadsheet-Style Delivery Tracking Interface 
● CRM & Client Data: 
○ FR-CRM1: Contact Deduplication & Merge Wizard 
○ FR-CRM2: WhatsApp Contact Field & Communications Opt-In 
○ FR-CRM3: Bi-Directional Pet & Owner Record Sync (ACS <> DevIntelle) 
Part 5: E-Commerce & Website Functional Requirements 
● FR-WEB1: Branch-Aware Stock Display 
● FR-WEB2: Product Variant Selection on Product Page 
● FR-WEB3: YouTube Video Embedding on Product Page 
● FR-WEB4: Consolidated One-Page Checkout 
● FR-WEB5: Bank Transfer Payment with Remittance Voucher Upload 
● FR-WEB6: Customer Profile with Order History, Tracking & Reviews 
● FR-WEB7: Themed Page Layouts (Home, Shop, New Arrivals, Clearance) 
● FR-WEB8: Branch-Aware WhatsApp Live Chat Integration 
● FR-WEB9: In-Stock Product Prioritization on Listings 
● FR-WEB10: Mandatory Field Indicator Style Customization 
● FR-WEB11: Individual Product Page WhatsApp Button 
Part 6: Backend & Administrative Functional Requirements 
● Clinic: 
○ FR-CLN1: Post-Consultation Editing of Vital Signs 
○ FR-CLN2: Post-Consultation Editing of Appointment Records 
● Laboratory & X-Ray: 
○ FR-LAB1: Lab Test & X-Ray Scan Data Models 
○ FR-LAB2: Automated PDF Report Generation & Storage 
● Accounting: 
○ FR-ACC1: Anglo-Saxon Chart of Accounts & FIFO Costing 
○ FR-ACC2: Legacy Invoice Archival Strategy 
○ FR-ACC3: Standardized Invoice-to-Payment Workflow 
○ FR-ACC4: Bulk Legacy Invoice Reconciliation Wizard 
● Human Resources (Phase 2 - Can also be done in Phase 1): 
○ FR-HR1: Employee Attendance & Time Tracking 
○ FR-HR2: Daily Staff Report Submission via Notes/Chat 
○ FR-HR3: Employee Payroll Module 
○ FR-HR4: Employee Appraisals Module 
● Marketing & Communications: 
○ FR-MRK1: Kudi SMS Integration & Marketing Automation 
○ FR-MRK2: Google Shopping Feed Integration 
● Miscellaneous Modules & Tools: 
○ FR-MSC1: Product Volume Calculation for Shipping 
○ FR-MSC2: Odoo Screen Recording for Support 
Part 7: The Closing Framework 
● 7.1. Non-Functional Requirements (Performance, Security, Usability, etc.) 
● 7.2. Data Migration Plan 
● 7.3. Assumptions, Risks, and Constraints 
● 7.4. Open Questions & Decisions Log 
● 7.5. Appendices (Wireframes, Source Docs, Glossary) 
Part 1: The Strategic Foundation 
This initial part establishes the strategic context. It answers why we are undertaking this 
project, who we are building it for, and what success looks like. This foundation is critical; 
every feature detailed in subsequent parts must align with these core principles. 
1.1. Executive Summary & Problem Statement 
● Executive Summary: Vetlane will consolidate its entire operational 
infrastructure—spanning two branches (Lagos & Abuja) 1, multiple business units 
(petshop, clinic, grooming, lab, X-ray) 2, and all sales channels (in-store, 
e-commerce)—onto a single, standardized Odoo 18 platform3. This initiative will unify 
disparate workflows, provide a single source of truth for all data, and create a superior, 
consistent customer experience. 4 
● The Core Problem ("The Why"): Vetlane's current operational model is a patchwork of 
disconnected systems and manual, error-prone processes that actively hinder growth 
and efficiency. This creates a high-friction environment for both staff and customers. 
○ Operational Inefficiency: The sales process is disjointed; Sales Reps cannot 
complete sales, forcing a handoff to a Cashier5. Product returns are destructive, 
requiring the deletion and recreation of entire sales records6, which compromises 
data integrity. Key business functions like grooming bookings 77 and delivery tracking 
8are managed in offline Excel spreadsheets, creating data silos and preventing 
real-time visibility. 
○ Data Fragmentation: There is no single view of the customer. Pet and client records 
are not synchronized between the clinic's ACS module and the grooming 
department's DevIntelle module. This leads to staff manually checking for duplicate 
contacts to avoid redundant data entry. Inventory management is unreliable, with no 
enforced system for tracking lots or expiry dates and no clear, real-time view of stock 
levels across different branches and POS locations. 
○ Poor Inventory Control: There is no enforced system for tracking lots and expiry 
dates 11, leading to potential waste and a lack of visibility on expiring products12. 
○ Sub-Optimal Customer Experience: The e-commerce platform fails to show 
branch-specific stock levels, leading to customer confusion. The checkout process is 
cumbersome, lacking a true one-page experience and a clear, integrated method for 
handling bank transfer payments. This friction directly impacts sales and customer 
satisfaction. 
○ Scalability Ceiling: The current infrastructure is not scalable. As Vetlane expands, 
these inefficiencies will compound, increasing operational costs and limiting the 
ability to launch new services or enter new markets. 
This project is not merely an upgrade; it is a foundational transformation to eliminate these 
systemic problems and build an efficient, scalable, and customer-centric business platform. 
1.2. Business Objectives & Measurable Success Metrics 
Every feature in this document must contribute to one or more of the following strategic 
objectives. Success is not subjective; it will be measured by the following KPIs. 
Objective 
Key Performance 
Target & Timeline 
Source(s) 
Indicator (KPI) 
Elevate 
E-Commerce 
Performance 
1. Online cart 
abandonment 
rate.    
2. Net Promoter 
Score (NPS) for 
e-commerce 
customers.   
3. Conversion rate 
for users who begin 
checkout.. 
1. Reduce by 30% 
within 6 months.   
2. Achieve and 
maintain a score of 
≥60.    
3. Increase by 15% 
within 6 months. 
16 
Achieve Total 
Inventory Control 
1. Lot & Expiry 
data capture rate 
on lot-tracked PO 
receipts. 
2. Stock 
discrepancy rate 
during cycle 
counts.    
3. Value of stock 
write-offs due to 
expiration. 
1. 100% 
compliance within 1 
month post-launch.    
 
2. Reduce by 50% 
within 3 months.   
 
 3. Reduce by 25% 
within 6 months. 
17 
Create a Single 
Source of Truth 
1. Rate of new 
duplicate 
customer records 
created.   
 2. Data 
synchronization 
errors between 
core modules (POS, 
Inventory, CRM, 
Clinic).    
3. Time to 
generate END OF 
1. Reduce by 95% 
within 2 months.    
2. 0 critical sync 
errors post-launch.    
3. Generate in      
30 seconds. 
18 
 
 
 
DAY EOD sales 
report by branch. 
Drastically 
Improve 
Operational 
Efficiency 
1. Time to process 
a POS 
return/exchange. 
2. Manual 
grooming booking 
entry.  
1. Reduce from >5 
mins to      90 
seconds within 1 
month.    
18 
2. Eliminate 100% 
of Excel-based 
entry from Day 1.    
3. End-of-day 
cash 
reconciliation 
time. 
1.3 Scope & Phased Implementation 
3. Reduce from >20 
mins to      3 mins 
within 1 month. 
To manage complexity and deliver value quickly, the project will be executed in phases. 
● Phase 1 (Core MVP): Focuses on establishing the foundational operational backbone. 
This includes the most critical features for POS, Inventory, Purchasing, core CRM, basic 
Grooming and Delivery management, and the revamped E-commerce site. All data 
migration is included here. 
● Phase 2 (Business Unit Expansion): Builds upon the core by fully implementing the 
customization of specialized modules for the Clinic, Laboratory, and X-Ray units. This 
phase also includes advanced accounting workflows and HR module implementation 
(Attendance, Payroll). 
● Phase 3 (Growth & Optimization): Focuses on strategic growth features, including 
advanced BI Analytics dashboards, marketing automation, and potentially a 
customer-facing mobile app. 
Explicitly Out of Scope for Phase 1: 
● A native customer-facing mobile application. 
● An integrated AI-assistant chatbot. 
● Advanced, customizable Business Intelligence (BI) dashboards 
● Full implementation of Odoo HR modules like Appraisals, Recruitment, and Employee 
Bonus (these are targeted for Phase 2). 
1.4 User Staff Personas & Roles 
Persona 
Core Responsibilities & System Interactions 
Sales 
Representative 
Cashier 
Supervisor 
Initiates transactions by scanning products and 
capturing customer details. Cannot edit prices or 
f
 inalize sales. Views branch stock levels. 
Finalizes sales, processes payments, prints receipts. 
Can process basic returns. Cannot edit completed 
invoices. 
The "floor manager" with override capabilities. Edits 
prices, deletes items from carts, processes complex 
exchanges, and adjusts delivery fees. 
Branch Manager Monitors overall branch performance. Views reports, 
manages stock levels, and initiates inter-branch 
transfers and scraps. 
Inventory Lead / 
Manager 
Grooming 
Coordinator 
Delivery Agent 
Manages the physical flow of goods. Receives 
supplier deliveries, enters lot/mfg/expiry data, updates 
product costs and sale prices, and identifies expiring 
stock. 
Manages the grooming business unit. Books and 
schedules appointments, looks up pet/owner records, 
and prints the daily work schedule. 
Executes the final step of fulfillment. Views their 
assigned deliveries in a list and updates the status of 
each delivery as it is completed. 
Source 
Document(s) 
E-commerce 
Customer 
Accountant 
Veterinary 
Clinician 
Purchasing 
Manager 
System 
Administrator 
Interacts with the public-facing website. Browses by 
branch, checks stock, views product videos, manages 
their profile, places orders, tracks delivery status, and 
writes reviews. 
Manages the company's financials within Odoo. 
Processes draft invoices, registers payments, 
reconciles bank statements and charges, and creates 
vendor bills from purchase orders. 
Manages the clinical aspects of the business. Records 
patient vital signs, orders lab tests and X-rays, reviews 
medical history, and can edit completed consultation 
records. 
Responsible for procurement. Creates RFQs, converts 
them to POs, and has the authority to update product 
cost and sale prices upon PO validation. 
Has overarching control of the system. Manages user 
roles and permissions, performs data imports/exports, 
and can perform corrective actions on POs. 
Part 2: POS Functional Requirements 
FR-POS1: Conditional Supervisor Approval Workflow 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a Sales Rep, I need to be able to build a customer's cart but flag it for 
a Supervisor's review if an error is found, so that mistakes in pricing or items can be 
corrected by an authorized user before payment is requested." 
● User Journey / Workflow: 
○ The Sales Rep adds items to the POS cart for a customer. 
○ The Sales Rep or customer identifies an error (e.g., wrong product, a discount 
was promised but not applied). 
○ The Sales Rep is unable to edit the price or remove the product directly. 
Instead, they click the "Flag for Supervisor" button. 
○ The POS order's status is immediately changed to "Awaiting Supervisor 
Approval." 
○ The order appears in a dedicated queue or list on the Supervisor's POS 
dashboard. 
○ The Supervisor selects the flagged order, makes the necessary corrections 
(edit price, change quantity, delete item), and then forwards the corrected 
order to the Cashier for payment processing. 
● UI/UX Considerations: 
○ A clearly visible "Flag for Supervisor" button will be present in the POS interface 
for the Sales Rep role. 
○ On the Supervisor's main POS screen, a dashboard component or badge will 
show the count of orders "Awaiting Supervisor Approval." Clicking this will filter 
the order list to show only those requiring action. 
○ Flagged orders will have a distinct visual indicator (e.g., a yellow border, an 
icon). 
● Acceptance Criteria: 
○ Given a user is logged in as a Sales Rep: 
■ When they add items to a cart, then the price and quantity fields for 
each line item are read-only. 
■ When they click the "Flag for Supervisor" button, then the order's 
status immediately changes to "Awaiting Supervisor Approval." 
○ Given an order has a status of "Awaiting Supervisor Approval": 
■ When a Supervisor views their POS dashboard, then the flagged order 
is visible in their approval queue. 
■ When the Supervisor opens the order, then they are able to edit 
quantities, prices, and delete line items. 
■ When the Supervisor completes their edits, then they have an option to 
"Send to Cashier," which moves the order to a standard 'ready for 
payment' state. 
● Roles & Permissions: 
○ Sales Rep: Can add items and flag orders. Cannot edit or complete. 
○ Supervisor: Can edit and approve flagged orders. 
○ Cashier: Cannot see the "Flag for Supervisor" button; only receives orders that 
are ready for payment. 
FR-POS2: POS Complete Sale by Cashier Only 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a Branch Manager, I must enforce a strict separation of duties where 
only a Cashier can finalize a sale and process payment, to ensure financial 
accountability and minimize risk." 
● User Journey / Workflow: 
○ An order is validated and ready for payment. 
○ The customer proceeds to the Cashier's station. 
○ The Cashier locates the order and confirms the total. 
○ The Cashier selects the customer's payment method (e.g., Bank Transfer, "Cash 
with Cashier") and clicks the "Complete Sale" button. 
○ The transaction is finalized, inventory deduction is triggered, and the official 
receipt is printed. 
● UI/UX Considerations: 
○ The "Payment" and "Complete Sale" buttons in the POS interface will be 
enabled only for users in the 'Cashier' or 'Supervisor' group. 
○ For Sales Reps, these buttons will be visually disabled (greyed out) and 
non-functional to provide a clear visual cue of their permissions. 
● Acceptance Criteria: 
○ Given a user is logged in as a Sales Rep: 
■ When they view a valid cart, then the "Payment" and "Complete Sale" 
buttons are visually disabled and non-functional. 
○ Given a user is logged in as a Cashier: 
■ When they open a valid cart, then the "Payment" and "Complete Sale" 
buttons are enabled and fully functional. 
■ When they click "Complete Sale," then the order state moves to "done," 
a receipt is generated for printing, and the associated inventory moves 
are created. 
● Roles & Permissions: 
○ Sales Rep: Cannot access payment or completion functions. 
○ Cashier / Supervisor: Can access payment and completion functions. 
FR-POS3: POS Product Returns & Exchanges Workflow 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a Supervisor, I need to efficiently process a return or exchange against 
a past purchase without deleting the original record, so that the customer experience 
is smooth and our inventory and financial records remain perfectly audited." 
● User Journey / Workflow: 
○ A customer presents an item for return/exchange. The Supervisor or Cashier 
looks up the original completed POS order. 
○ They select the "Return/Exchange" action available on the completed order. 
○ A modal window appears, listing the items from the original sale. 
○ The user specifies the quantity to be returned for each relevant item and 
selects a reason from a dropdown. 
○ For an exchange, they add the new replacement product to the return order. 
○ The system calculates the financial difference. 
○ Upon confirmation, the system creates all necessary backend transactions: 
negative stock moves for returns, positive for new items, and a linked credit 
note for auditing. 
● UI/UX Considerations & Wireframe Description: 
○ On a completed POS order screen, a "Return/Exchange" button will be visible to 
authorized roles. 
○ The modal will clearly display the original order details (product image, name, 
quantities, prices) for context. 
○ It will feature an input field for return_quantity next to each item, which 
cannot exceed the original_quantity. 
○ For exchanges, a product search/add field will allow the user to add the new 
item(s) to the transaction. 
○ The modal will show a running summary of the financial impact (e.g., "Amount 
to Refund: ₦5,000" or "New Amount Due: ₦2,500"). 
● Acceptance Criteria: 
○ Given a user is viewing a completed POS order: 
■ When they initiate a "Return" for one item, then a new POS order with a 
negative quantity for that item is created, and inventory for that item is 
increased. 
■ When they initiate an "Exchange" for one item with another, then the 
returned item's stock is increased, the new item's stock is decreased, 
and a credit note and/or new invoice line reflects the financial 
difference. 
■ When any return/exchange is processed, then the new transaction is 
explicitly linked to the original order number. 
■ When a user tries to return a quantity greater than what was originally 
purchased, then the system presents an error and blocks the action. 
● Roles & Permissions: 
○ Cashier: Can process simple returns. 
○ Supervisor: Can process both simple returns and complex exchanges. 
FR-POS4: Role-Based Cost Price Suppression 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As the business owner, I must prevent Sales Reps and Cashiers from 
seeing product cost prices anywhere in the POS system, to protect sensitive margin 
information and maintain pricing integrity." 
● User Journey / Workflow: 
○ A Sales Rep or Cashier logs into the POS and adds products to an order. 
○ The system, aware of their role, renders the POS interface without any columns, 
f
 ields, or information related to the product's cost. 
○ A Supervisor or Admin logs into the back-office of Odoo. 
○ When they view POS reports or product information, the cost and margin 
details are fully visible to them. 
● UI/UX Considerations: 
○ This is a subtractive requirement. For non-privileged roles, the "Cost" column in 
the POS order grid and product info views will not be rendered in the DOM. 
○ In the back-office POS reports, toggles will be available for privileged roles to 
show/hide cost and margin columns for cleaner reporting views. 
● Acceptance Criteria: 
○ Given a user is logged in with the 'Sales Rep' or 'Cashier' role: 
■ When they view the POS screen or search for products, then no field or 
column displaying the product 'Cost Price' is visible. 
○ Given a user is logged in with the 'Supervisor' or 'Admin' role: 
■ When they view the POS back-office reports, then they can see 
columns for 'Cost', 'Margin', and 'Total Margin'. 
■ When they view product details in the back-office, then the 'Cost' field 
is visible and populated. 
● Roles & Permissions: 
○ Sales Rep / Cashier: No visibility of cost price. 
○ Supervisor / Admin: Full visibility of cost price in the back-office. 
FR-POS5: Multi-Branch Stock Display & Fulfillment Selection 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a Sales Rep assisting a customer, I need to see the real-time stock 
quantities for a product at both the Lagos and Abuja branches simultaneously, so I can 
accurately inform the customer about availability and, if needed, have a Supervisor 
arrange fulfillment from another branch." 
● User Journey / Workflow: 
○ A Sales Rep searches for a product in the POS. 
○ The search results and the product line in the cart display the on-hand quantity 
for each branch (e.g., "Lagos: 12 | Abuja: 5"). 
○ The POS session defaults to fulfilling from the user's local branch warehouse. 
○ If an item is out of stock locally but available elsewhere, a Cashier or Supervisor 
can use a "Fulfill From" dropdown in the POS header to select the other 
branch's warehouse for that specific order. 
● UI/UX Considerations: 
○ Small, non-intrusive "stock badges" will appear under the product name in the 
POS grid. 
○ A dropdown menu, possibly with a location icon, will be present in the POS 
header. It will display the current fulfillment branch and allow authorized users 
to change it. 
○ The POS product loading mechanism will be modified to perform a 
multi-warehouse stock lookup for each product displayed. This must be 
optimized to prevent slowing down the POS interface. 
● Acceptance Criteria: 
○ Given a user is logged into the Lagos branch POS: 
■ When they add a product to the cart, then they see stock levels for 
both Lagos and Abuja. 
■ When a Sales Rep views the POS header, then the "Fulfill From" 
dropdown is visible but read-only, defaulted to 'Lagos Warehouse'. 
○ Given a user is logged in as a Cashier or Supervisor: 
■ When they view the POS header, then the "Fulfill From" dropdown is 
editable. 
■ When they change the fulfillment warehouse from 'Lagos' to 'Abuja' and 
complete the sale, then the stock deduction occurs from the 'Abuja 
Warehouse' inventory. 
● Roles & Permissions: 
○ Sales Rep: View-only access to branch stock levels. 
○ Cashier / Supervisor: Can override the fulfillment branch for an order. 
FR-POS6: Bulk User Roles Import via CSV 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a System Administrator, I need to bulk-create and assign roles to new 
POS staff using a predefined CSV template, to dramatically speed up the onboarding 
process for new hires." 
● User Journey / Workflow: 
○ The Admin navigates to Odoo Settings -> Users. 
○ They find an "Import POS Roles" action. 
○ They download the vetlane_pos_roles.csv template. 
○ They populate the template with new user data (username, role label like 
'Salesperson', 'Cashier', 'Supervisor'). 
○ They upload the completed CSV file. 
○ The system shows a preview, validates the data, and maps the role labels to the 
correct Odoo security groups. 
○ The Admin confirms the import, and the new user accounts are created with 
the appropriate POS permissions. 
● UI/UX Considerations: 
○ The UI will be a standard Odoo import wizard. 
○ It will feature a file upload widget, a data preview grid, and validation feedback 
(e.g., "Row 5: Invalid role label 'Salesman'"). 
● Acceptance Criteria: 
○ Given an Admin uploads a valid CSV with a new user and the role 'Salesperson': 
■ When they complete the import, then a new user is created and 
assigned to the 'Point of Sale User' group. 
○ Given an Admin uploads a CSV with an invalid role label: 
■ When they attempt the import, then the system presents an error 
message identifying the invalid row and does not import that row. 
● Roles & Permissions: 
○ System Administrator: The only role with access to this import functionality. 
FR-POS7: Live Customer Search & Inline Creation 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a Sales Rep, I need to quickly search for existing customers by name, 
phone, or email, or create a new customer record inline, to ensure every transaction is 
linked to a customer and to prevent creating duplicate records." 
● User Journey / Workflow: 
○ At the start of a new POS order, the "Customer" field is the first active element. 
○ The user begins typing a customer's name, phone number, or email address. 
○ The system displays an autocomplete dropdown list of matching customers in 
real-time. 
○ If the correct customer appears, the user selects them, and the customer is 
associated with the order. 
○ If no match is found, a "+ Create" option appears in the dropdown. Clicking it 
opens a minimal inline form to create a new customer record without leaving 
the POS screen. 
● UI/UX Considerations: 
○ A prominent "Customer" field will be located at the top of the POS screen. 
○ The autocomplete search will be fast and responsive. 
○ The inline creation form will only contain essential fields (e.g., Name, Phone, 
Email) to ensure a speedy process. 
● Acceptance Criteria: 
○ Given a user starts a new POS order: 
■ When they type a known customer's phone number into the customer 
search field, then that customer's record appears in the autocomplete 
results. 
■ When no customer is found and the user clicks "+ Create," then an 
inline form appears allowing for new customer creation. 
■ When a new customer is created, then that customer is immediately 
assigned to the current POS order. 
● Roles & Permissions: 
○ Sales Rep / Cashier / Supervisor: All have full access to search for and create 
customers from the POS interface. 
FR-POS8: Post-Sale Transaction Lock 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As an Accountant, I must ensure that once a POS order is completed and 
paid, it is permanently locked from any further edits or deletion by any user, to 
guarantee the absolute integrity of our financial and sales records for auditing." 
● User Journey / Workflow: 
○ A Cashier completes a sale, and the pos.order state moves to "done". 
○ At this moment, the system makes the entire record read-only. 
○ If any user, including a System Administrator, later views this completed order, 
all fields are disabled, and the "Edit" and "Delete" buttons are removed or 
greyed out. 
○ The only way to correct a mistake is to use the formal "Return/Exchange" 
workflow (FR-POS3). 
● UI/UX Considerations: 
○ On completed orders, the "Edit" and "Delete" buttons will be hidden or 
disabled. 
○ A tooltip will appear on hover over the disabled buttons stating: "Completed 
orders are locked. To correct, please use the Return/Exchange workflow or 
recreate the sale.". 
● Acceptance Criteria: 
○ Given a POS order is in the 'done' state: 
■ When a System Administrator attempts to edit the order, then the 
action is blocked, and all fields remain read-only. 
■ When any user attempts to delete the order, then the action is blocked, 
and an error message or tooltip explains why. 
● Roles & Permissions: 
○ All Users (including Admin): Cannot edit or delete a completed POS order. 
FR-POS9: "Cash with Cashier" Custom Payment Method 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx. 
● User Story: "As a Cashier, I need a dedicated payment method called 'Cash with 
Cashier' to post all physical cash transactions, to prevent POS session closing errors 
and to clearly segregate cash for end-of-day reconciliation." 
● User Journey / Workflow: 
○ This is a one-time configuration task performed by the System Administrator. 
○ A new payment method is created in the POS settings. 
○ When a Cashier processes a payment, they see and select the "Cash with 
Cashier" option for all cash transactions. 
● UI/UX Considerations: 
○ On the POS payment screen, the button for cash payments will be explicitly 
labeled "Cash with Cashier.". The default "Cash" method will be deactivated. 
● Acceptance Criteria: 
○ Given the POS system is configured: 
■ When a Cashier proceeds to the payment screen, then they see a 
payment option labeled "Cash with Cashier." 
■ When a payment is made using this method, then the resulting journal 
entry is posted to the designated "Cash with Cashier" bank journal. 
● Roles & Permissions: 
○ System Administrator: Configures the payment method. 
○ Cashier / Supervisor: Can select and process payments with this method. 
FR-POS10: End-of-Day Cash Deposit & Reconciliation Wizard 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx. 
● User Story: "As a Supervisor, at the end of each day, I need a simple wizard to 
aggregate all payments recorded as 'Cash with Cashier', create a single bank deposit 
statement, and reconcile those payments, to ensure our books are balanced daily with 
minimal manual effort." 
● User Journey / Workflow: 
○ At the end of the day, the Supervisor navigates to the POS Dashboard. 
○ They launch the "End-of-Day Deposit" wizard. 
○ The wizard automatically calculates and displays the total amount from all 
unreconciled "Cash with Cashier" payments. 
○ The Supervisor verifies the amount, enters a reference (e.g., deposit slip 
number), and confirms. 
○ The system automatically creates a bank statement line for the deposit and 
reconciles all the individual cash payments against it. 
● UI/UX Considerations: 
○ An "End-of-Day Deposit" button will be available in the POS Dashboard 
reporting section. 
○ The wizard will be a simple form with fields for Date, calculated Amount, and a 
Reference text field. 
● Acceptance Criteria: 
○ Given there are ₦50,000 worth of "Cash with Cashier" payments for the day: 
■ When the Supervisor opens the End-of-Day Deposit wizard, then the 
amount field is pre-populated with ₦50,000. 
■ When the Supervisor confirms the wizard, then a new bank statement 
line for ₦50,000 is created. 
■ And all the individual cash pos.payment records for that day are 
marked as reconciled. 
● Roles & Permissions: 
○ Cashier / Supervisor: Can access and run the wizard. 
FR-POS11: Supervisor In-Cart Item Deletion 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a Supervisor, when called over by a Sales Rep, I need the clear 
authority to delete an item from a customer's cart before the sale is completed, to 
quickly correct mistakes and keep the checkout process moving." 
● User Journey / Workflow: 
○ A Sales Rep adds an incorrect item to the cart. 
○ They are unable to remove it and call over a Supervisor. 
○ The Supervisor uses their credentials (either by logging into the POS session or 
via a pop-up authentication) and can now click the standard 'delete' icon on 
the POS order line to remove the item. 
● UI/UX Considerations: 
○ The 'delete' or 'trash' icon on a POS order line will be visually disabled (greyed 
out) and non-functional for users in the Sales Rep role. 
○ The same icon will be enabled and fully functional for users in the Supervisor 
role. 
● Acceptance Criteria: 
○ Given a Sales Rep has added an item to the cart: 
■ When they click the delete icon on the order line, then nothing 
happens. 
○ Given a Supervisor is viewing the same cart: 
■ When they click the delete icon on the order line, then the item is 
removed from the cart and the order total is updated. 
● Roles & Permissions: 
○ Sales Rep: Cannot delete items from the cart. 
○ Supervisor: Can delete items from the cart. 
FR-POS12: Dynamic Delivery Fee Editing in POS 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a Supervisor, I need to be able to edit only the delivery fee on a POS 
order without changing any product prices, to accommodate special delivery 
circumstances or manual shipping quotes that differ from the system's standard rates." 
● User Journey / Workflow: 
○ A delivery method is added to the POS order, which adds a "Delivery" product 
line with a standard price. 
○ The Supervisor determines a special rate is needed. 
○ They click on the price field for the "Delivery" line item only. 
○ They can edit this price, and the order total updates accordingly. The price 
f
 ields for all other standard products remain locked. 
● UI/UX Considerations: 
○ The price field on the pos.order.line for any product configured as a 
"Delivery" service will be editable for the Supervisor role. 
○ For all other standard products, the price field will remain read-only for all roles 
in the POS (per FR-POS1 and FR-POS11). 
● Acceptance Criteria: 
○ Given an order contains a standard product and a delivery line item: 
■ When a Supervisor clicks on the price of the standard product, then the 
f
 ield is not editable. 
■ When a Supervisor clicks on the price of the delivery line item, then the 
f
 ield becomes editable and they can change the value. 
■ When a Sales Rep views the same order, then the price fields for both 
the standard product and the delivery line are not editable. 
● Roles & Permissions: 
○ Sales Rep / Cashier: Cannot edit the delivery price. 
○ Supervisor: Can edit the delivery price. 
Part 3: Inventory, Purchasing & Procurement Functional Requirements 
FR-INV1: Multi-Branch Warehouse & Internal Location Configuration 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As an Operations Manager, I need to configure our Odoo system to mirror 
our physical reality, with distinct warehouses for Lagos and Abuja, and separate 
internal stock locations for each petshop, clinic, lab, and X-ray unit, so that every 
single item is tracked to its precise location." 
● User Journey / Workflow: 
○ This is a one-time administrative setup task. 
○ The System Administrator navigates to the Inventory configuration settings. 
○ They create two primary warehouses: WH/LAG (Vetlane Lagos) and WH/ABJ 
(Vetlane Abuja) and attach it to the Respective Branch Name 
○ Within each warehouse, they create specific internal locations. 
○  For Abuja, under WH/ABJ, they will create: 
■ WH/ABJ/Stock (Main Warehouse Stock) 
■ WH/ABJ/Shop1 (Dog Petshop Sales Floor) 
■ WH/ABJ/Shop2 (Cat Petshop Sales Floor) 
■ WH/ABJ/Clinic (Clinic-use Stock) 
■ WH/ABJ/Lab (Lab-use Stock) 
■ WH/ABJ/Xray (X-ray-use Stock) 
■ WH/ABJ/Scrap (Scrap and Damaged Items Stock) 
○ For Lagos, under WH/LAG, they will create: 
■ WH/LAG/Stock (Main Warehouse Stock) 
■ WH/LAG/Shop1 (Petshop Sales Shop) 
■ WH/LAG/Scrap (Scrap and Damaged Items Stock) 
● UI/UX Considerations: 
○ The location structure in Odoo should be hierarchical and clearly named to be 
easily understood by all staff during transfers or inventory lookups. 
○ Each location will have its own inventory valuation and on-hand quantity 
reports. 
○ Routes will be configured to manage the flow of goods (e.g., from 
WH/ABJ/Stock to WH/ABJ/Shop1). 
● Acceptance Criteria: 
○ Given the system is configured: 
■ When an Administrator views the Warehouse locations, then they see 
two main warehouses for Lagos and Abuja. 
■ When they expand the Abuja warehouse, then they see separate 
internal locations for the two petshops, clinic, lab, and X-ray units as 
specified. 
● Roles & Permissions: 
○ System Administrator: Configures warehouses and locations. 
FR-INV2 & FR-INV3: Mandatory & User-Friendly Lot/Expiry Date Entry on POs 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As an Inventory Lead, I need the system to force me to enter Lot Number, 
Manufacturing Date, and Expiry Date when receiving products, and the interface must 
be clear and easy to use, even for a single product line that arrives in multiple lots, so 
that all inventory is tracked for freshness and we can eliminate human error." 
● User Journey / Workflow: 
○ The Inventory Lead opens a Purchase Order to receive goods. 
○ For each product line that is tracked by lots, the input fields for 'Lot Number', 
'Mfg Date', and 'Expiry Date' are visible and required directly on the line. 
○ If a single product line (e.g., 100 units of dog food) arrives with multiple lot 
numbers/expiry dates, the user clicks on a "Lots" tab for that specific line. 
○ Inside the "Lots" tab, they use a simple grid to add multiple lines, one for each 
lot, specifying the quantity, lot number, and dates for each partial quantity. 
○ The system will not allow the receipt to be validated if any lot-tracked product 
is missing this information. 
● UI/UX Considerations & Wireframe Description: 
○ Inline Fields: The main purchase order line view will be modified to include 
editable columns for "Lot/Serial Number", "Mfg Date", and "Expiry Date" 
directly next to the quantity field. This makes single-lot entry fast and 
impossible to miss. 
○ Multi-Lot Tab: For handling multiple lots per line, each PO line's detailed form 
view will contain a notebook with a prominent "Lots" tab. This tab will contain 
an editable one-to-many list (a grid) with columns for "Lot/Serial Number", 
"Quantity", "Mfg Date", and "Expiry Date". 
○ The mockup provided in the source documents shows exactly this two-part 
layout: a main table with inline fields for the simple case, and a secondary table 
within a "Lots" tab below for the complex case. This design will be 
implemented. 
○ On PO confirmation, if the "Lots" grid is used, the system will generate separate 
stock.move.line records for each entry in the grid, ensuring each lot is its 
own unique entity in the inventory system. 
○ The system will prevent entry of an expiry date that is in the past. 
● Acceptance Criteria: 
○ Given a product is configured to be tracked by lots: 
■ When a user tries to validate a PO receipt for this product without filling 
in the Lot and Expiry Date fields, then the system shows a validation 
error and blocks the confirmation. 
■ When a user receives 100 units of the product that came in two lots of 
50, then they can use the "Lots" tab to create two separate lot entries 
for that single PO line. 
■ When the PO is validated, then the system creates two distinct quants 
in inventory, one for each lot, with their respective expiry dates. 
● Roles & Permissions: 
○ Inventory Lead / Purchasing Manager: Responsible for entering this data 
during goods receipt. 
FR-INV4: Expiry Products – Alert Dashboard & Cron Job 
Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements Document 
(PRD)current.docx. 
USER STORY 
■ As an Inventory Manager, I need a dashboard that automatically shows me all products 
approaching expiry so I can plan promotions or prioritize their sale to minimize financial losses 
from expired stock. 
USER JOURNEY / WORKFLOW 
■ A scheduled job (cron job) runs every night 
○ Scans all product lots in inventory (stock.production.lot) 
○ Flags any lot whose remaining days to expiry ≤ configured alert threshold 
■ Inventory Manager navigates to Inventory → Reports → Expiring Soon 
■ The Report view lists only flagged lots, showing: 
○ Product 
○ Lot Number 
○ Quantity on Hand 
○ Expiry Date 
○ Days Remaining 
TECHNICAL EXPLANATION & RATIONALE 
CRON JOB & FLAGGING LOGIC 
■ What it does: every night, a scheduled cron scans all lots in stock.production.lot 
■ Flag rule: if (expiry_date – today) ≤ alert_threshold_days → set is_expiring_soon = True 
■ Purpose: highlights only lots within the manager’s chosen window 
THRESHOLD CONFIGURATION 
■ Default threshold: 60 days 
■ Override: manager may set global threshold (e.g., 30, 45, 90 days) 
■ Impact: all flagging and display respect the current threshold 
COLOR-CODING BANDS 
■ We dynamically split the flagged window into three urgency levels based on the current 
threshold: 
○ Green (“Safe”): Days Remaining > two-thirds of threshold 
○ Yellow (“Warning”): Days Remaining > one-third and ≤ two-thirds of threshold 
○ Red (“Critical”): Days Remaining ≤ one-third of threshold 
■ Why dynamic? bands scale with threshold changes, preserving relative urgency 
BOUNDARY CONDITIONS 
■ Exactly on threshold (e.g., 60 days left when threshold = 60) → flagged 
■ Just outside threshold (e.g., 61 days left when threshold = 60) → not flagged 
■ Expired (negative days remaining) → not flagged; handled in Expired Stock report 
UI BEHAVIOR 
■ Menu visibility: Inventory → Reports → Expiring Soon (authorized roles only) 
■ Report table columns: Product; Lot Number; Quantity; Expiry Date; Days Remaining; 
Urgency indicator 
■ Urgency indicator: colored cell with tooltip “X days remaining” on hover 
■ Empty state: “No products approaching expiry” message and “Refresh” button 
PERFORMANCE & LOGGING 
■ Performance: cron completes within 5 minutes when scanning up to 100,000 lots 
■ Logging: each run writes to stock.expiry.log with timestamp, threshold value, and 
f
 lagged-lot count; on failure, retries up to three times and logs errors 
UI/UX CONSIDERATIONS 
■ New menu item “Expiring Soon” under Inventory → Reporting 
■ Color bands adapt to threshold: 
○ Green: > two-thirds of threshold left 
○ Yellow: between one-third and two-thirds left 
○ Red: ≤ one-third left 
■ Tooltips: “X days remaining” 
■ Empty view: show “No products approaching expiry” and “Refresh” button 
ACCEPTANCE CRITERIA 
■ Scenario: Flag a lot within threshold 
○ Given a product lot with 45 days remaining 
○ And alert_threshold = 60 days 
○ When the nightly cron job runs 
○ Then is_expiring_soon = True; Days Remaining = 45; Urgency = Yellow 
○ And the lot appears in the Expiring Soon report 
■ Scenario: Exclude a lot beyond threshold 
○ Given a product lot with 75 days remaining 
○ And alert_threshold = 60 days 
○ When the nightly cron job runs 
○ Then is_expiring_soon = False 
○ And the lot does not appear in the Expiring Soon report 
■ Scenario: Boundary at exact threshold 
○ Given a product lot with exactly 60 days remaining 
○ And alert_threshold = 60 days 
○ When the nightly cron job runs 
○ Then is_expiring_soon = True; Urgency = Yellow 
○ And the lot appears in the Expiring Soon report 
■ Scenario: Flad already expired lot Until It is Removed  
○ Given a product lot expired 1 day ago 
○ When the nightly cron job runs 
○ Then is_expiring_soon = True 
○ And the lot does not appear in the Expiring Soon report 
■ Scenario: Dynamic threshold adjustment 
○ Given default threshold = 60 days 
○ And manager updates alert_threshold = 45 days 
○ When the nightly cron job runs 
○ Then only lots with Days Remaining ≤ 45 are flagged 
○ And urgency bands are recalculated on the 45-day window 
■ Scenario: Color-coding scaling 
○ Given alert_threshold = 60 days and flagged lots with 50, 30, and 10 days remaining 
○ When viewing the Expiring Soon report 
○ Then Urgency = Green for 50 days; Yellow for 30 days; Red for 10 days 
○ And hovering shows “50 days remaining,” “30 days remaining,” “10 days remaining” 
■ Scenario: Empty-state display 
○ Given no lots are flagged 
○ When opening the Expiring Soon report 
○ Then display “No products approaching expiry” 
○ And show “Refresh” button 
■ Scenario: Access control 
○ Given a user with the Inventory User role 
○ When they navigate to Inventory → Reports → Expiring Soon 
○ Then the menu item is hidden 
○ And if they request the URL directly, they see “Access Denied” 
ROLES & PERMISSIONS 
■ Inventory Lead / Branch Manager: can view the Expiring Soon report 
■ Inventory User: cannot view the Expiring Soon report 
FR-INV5: Inter-Branch Transfer with Receipt Confirmation 
● Source(s): Vetlane Erp Comprehensive PRD (1).docx. 
● User Story: "As a Branch Manager, I need a formal, auditable process for transferring 
stock between branches, where the receiving party must electronically sign for and 
confirm the quantities received, and log any discrepancies, to ensure full 
accountability for all stock movements." 
● User Journey / Workflow: 
○ The Inventory Manager at the sending branch (e.g., Lagos) initiates an "Internal 
Transfer" in Odoo. 
○ They specify the source location (e.g., WH/LAG/Stock) and the destination 
location (e.g., WH/ABJ/Stock). 
○ The transfer appears on the dashboard of the receiving branch's manager as 
"Pending Receipt." 
○ When the physical goods arrive, the Shop Supervisor at the receiving branch 
opens the pending transfer. 
○ They count the items and enter the actual quantities received. 
○ If there is a discrepancy between the sent and received quantities, the system 
requires the Supervisor to select a "Discrepancy Reason" (e.g., 'Damaged in 
Transit', 'Short-shipped') and add notes. 
○ The Supervisor confirms the receipt using a "Receiver Signature" field (a digital 
signature pad). 
○ The system completes the transfer, updating stock levels in both locations 
according to the received quantities. 
● UI/UX Considerations: 
○ The standard Odoo internal transfer wizard will be enhanced. 
○ A "Receiver Signature" field (using a digital signature widget) will be added to 
the validation step of the receipt. 
○ A "Discrepancy Reason" dropdown and a "Notes" text field will be added, which 
become mandatory if quantity_done != quantity_initial. 
○ The dashboard will feature a clear status indicator for transfers, such as 
"Pending Receipt." 
● Business Logic & Technical Considerations: 
○ The system will generate two stock.picking records: one for the outgoing 
shipment from the source and one for the incoming receipt at the destination. 
○ The final stock adjustment will be based on the quantities confirmed by the 
receiver, not the quantities initially sent. 
○ All details—discrepancy reasons, notes, and the captured signature—will be 
logged in the chatter and audit trail of the transfer record. 
● Acceptance Criteria: 
○ Given an internal transfer is created from Lagos to Abuja for 10 units of a 
product. 
○ When the Abuja Supervisor enters a received quantity of 9, then the system 
must make the "Discrepancy Reason" field mandatory before they can confirm 
the receipt. 
○ When the receipt is confirmed with the signature, then the stock at the Lagos 
location decreases by 10 (as sent), the stock at the Abuja location increases by 
9 (as received), and a scrap/loss of 1 is recorded, linked to the transfer. 
○ And the transfer record must show the receiver's signature and the logged 
discrepancy reason. 
● Roles & Permissions: 
○ Inventory Manager / Branch Manager: Can initiate transfers. 
○ Shop Supervisor (at receiving end): Can confirm receipt, log quantities, and 
record discrepancies. 
FR-PUR1: Simplified Purchase Module UI/UX 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a Purchaser, I need the Purchase Order screen to be simplified by 
default, hiding complex fields and organizing tabs logically, so I can create RFQs and 
POs quickly and with fewer distractions." 
● User Journey / Workflow: 
○ A Purchaser opens a new RFQ/PO form. 
○ The view they see is clean and focused. Advanced tabs like "Analytical" are 
hidden by default within a collapsible "Advanced" section. 
○ The visible tabs are ordered for a logical workflow: Main fields first, then "Lot 
Details," then "Notes." 
○ Hovering over key fields displays contextual tooltips with brief help text. 
● UI/UX Considerations: 
○ The PO form view will be modified using Odoo Studio or custom XML. 
○ A collapsible section labeled "Advanced Options" will contain less frequently 
used fields and tabs. 
○ Inline help text (help="..." attribute in field definitions) will be added to 
critical fields like "Vendor" and "Desired Date." 
● Business Logic & Technical Considerations: 
○ This is primarily a view-level customization. No complex business logic is 
required. 
● Acceptance Criteria: 
○ Given any user opens a new Purchase Order form: 
■ When the form loads, then the "Analytical" and other advanced tabs 
are not immediately visible. 
■ When the user looks at the tabs, then they are in the order of "Main 
Fields," "Lot Details," "Notes." 
■ When the user hovers their mouse over the "Desired Date" field, then a 
tooltip explaining the field's purpose appears. 
● Roles & Permissions: 
○ Purchaser / Admin: This UI change applies to all users of the purchase 
module. 
FR-PUR2: Admin Purchase Order (PO) Editing, Reversal & Audit Trail 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a System Administrator, I need the ability to unlock and edit a 
confirmed Purchase Order to correct significant mistakes, and I also need a formal 
'Reverse PO' function to cleanly back out an incorrect transaction while maintaining a 
perfect, auditable log of every change." 
● User Journey / Workflow: 
○ Editing: An Admin opens a confirmed PO and sees it's locked. They toggle an 
"Enable Admin Edit" switch in the form header. All fields become editable. The 
system logs every field change (old value, new value, user, timestamp) to an 
"Audit Trail" tab on the PO form. 
○ Reversal: For a major error (e.g., wrong vendor), the Admin uses a "Reverse 
PO" action. The system automatically creates a new, negative-value PO that 
perfectly cancels out the original, and links the two. 
○ Recreation: After reversing, the Admin can use a "Recreate PO" wizard to clone 
the details of the original (or reversed) PO into a new draft, allowing for quick 
correction and resubmission. 
● UI/UX Considerations: 
○ An "Enable Admin Edit" switch/button will appear in the PO form header, visible 
only to Admins. 
○ "Reverse PO" and "Recreate PO" buttons will also appear in the header when in 
Admin Edit mode. 
○ A new "Audit Trail" tab will be added to the PO form's notebook, displaying a log 
of all changes. 
● Business Logic & Technical Considerations: 
○ The "Enable Admin Edit" toggle will temporarily override the readonly states 
on the form view. 
○ The system will use a po.audit.log model to store changes. Each write() 
call on the PO while in edit mode will trigger the creation of new log entries. 
○ The "Reverse PO" action will execute a script to create a new PO with inverted 
quantities and amounts and establish a many2one link between the original and 
the reversal. 
● Acceptance Criteria: 
○ Given an Admin is viewing a confirmed PO: 
■ When they toggle "Enable Admin Edit" and change the quantity of a line 
from 10 to 12, then the change is saved, and the "Audit Trail" tab shows 
a new entry: "Field: Quantity, Old: 10, New: 12, User: Admin, Timestamp: 
[current_time]." 
■ When they click "Reverse PO," then a new, linked PO is created with 
credit-only entries that financially cancel the original. 
■ When they use the "Recreate PO" wizard, then a new draft PO is 
created, pre-populated with the details of the original for quick editing. 
● Roles & Permissions: 
○ System Administrator (base.group_system): Can use the edit, reverse, and 
recreate functions. 
○ Other users: Cannot see or use these functions. 
Part 3: Inventory, Purchasing & Procurement Functional Requirements  
FR-PUR3: Automatic Sales Price Update from Purchase Order (PO) Line 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a Purchasing Manager, when I negotiate a new price for a product, I 
want to update its standard sales price for all customers directly from the Purchase 
Order form, so that our pricing is always current and reflects our latest costs without 
needing to perform a second data entry step." 
● User Journey / Workflow: 
○ The Purchasing Manager creates a Purchase Order. 
○ On the PO line for a product, alongside the Unit Cost, there is an editable 
Sale Price field. 
○ The Manager enters the new intended sales price in this field. 
○ Upon validating the PO, the system presents a confirmation prompt: "Update 
this product's sales price to [New Price] for all channels?". 
○ The Manager confirms. The system then updates the list_price field on the 
main product template. 
○ This new price is immediately live on the e-commerce site and in the POS 
system for all new transactions. 
● UI/UX Considerations: 
○ An editable Sale Price column with a currency widget will be added to the 
PO line view. 
○ A clear, non-intrusive confirmation dialog will appear upon PO validation to 
prevent accidental price changes. 
● Business Logic & Technical Considerations: 
○ This action directly overrides the product.template.list_price. 
○ This serves as the new base price. Any existing, more specific pricelist rules 
(e.g., "10% off for VIP customers") will still apply and will calculate based on this 
new base price. The pricelist items themselves are not affected. 
● Acceptance Criteria: 
○ Given a product has a sales price of ₦1000. 
○ When a Purchasing Manager validates a PO with a new Sale Price of ₦1200 
for that product and confirms the prompt, then the list_price on the 
product's form immediately updates to ₦1200. 
○ And when any user views that product on the website or adds it to the POS 
cart, then the displayed price is ₦1200 (unless a specific pricelist rule 
overrides it). 
● Roles & Permissions: 
○ Purchasing Manager / Admin: Can enter the new sale price and confirm the 
update. 
FR-PROC1: Formalized Goods Receipt Notice (GRN) & Quality Control QC Workflow 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx. 
● User Story: "As a Warehouse Manager, I need a formal goods receipt process that 
includes a distinct Quality Control step, so that we can verify shipments accurately, 
check for damages, and prevent subpar products from ever entering our sellable 
stock." 
● User Journey / Workflow: 
○ Initiate Receipt: The Warehouse Manager opens a confirmed PO and 
generates a Goods Receipt Notice (GRN). This creates an incoming 
stock.picking. 
○ Quality Control: A QC Officer takes over. They scan each item's barcode, and 
the system verifies the SKU and quantity against the GRN. They complete a 
digital QC checklist on the GRN form (e.g., 'Packaging Integrity: Pass/Fail', 
'Weight Verified: Pass/Fail'). 
○ Hold/Adjust: If an item fails QC, the officer marks it as 'On Hold', enters a 
reason, and notifies the Warehouse Manager via the Odoo chatter. Items on 
hold are not added to available stock. 
○ Finalize Receipt: Once all items have passed QC, the Warehouse Manager 
gives final confirmation. The system then processes the stock moves to 
increase inventory quantities for the passed items. 
● UI/UX Considerations: 
○ The GRN form will include a 'Start Quality Control QC' button. 
○ A grid will display scanned vs. expected item counts. 
○ QC checklist fields (Pass/Fail toggles) with a comments box will be on the form. 
○ Items failing QC will have a clear 'On Hold' tag next to them. 
● Business Logic & Technical Considerations: 
○ The GRN cannot be moved to the 'Done' state until all line items have passed 
their QC checks. 
○ Items marked 'On Hold' will be moved to a virtual "Quarantine" location instead 
of the main stock location. 
● Acceptance Criteria: 
○ Given a PO has been converted to a GRN. 
○ When the QC Officer scans an item that is not on the GRN, then the system 
produces an error sound/message. 
○ When an item is marked as 'Fail' on the QC checklist, then its status changes 
to 'On Hold' and it cannot be received into main stock. 
○ When the QC Officer posts a failure note to the chatter, then the Warehouse 
Manager receives a notification. 
● Roles & Permissions: 
○ Warehouse Manager (stock.group_stock_manager): Can initiate and 
f
 inalize the GRN. 
○ Quality Control QC Officer (stock.group_stock_user): Can perform 
scans and complete QC checks. 
FR-PROC2: Streamlined Request for Quotation (RFQ) Workflow 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx. 
● User Story: "As a Purchaser, I need a simple way to create a Request for Quotation, 
email it to a vendor, and have my manager approve and convert it to a Purchase Order, 
all within a single, easy-to-follow workflow." 
● User Journey / Workflow: 
○ A Purchaser creates a new RFQ, filling in a minimal form (Vendor, Product, 
Quantity). Advanced fields are collapsed by default. 
○ They click 'Send RFQ'. The system auto-generates a PDF of the RFQ and emails 
it to the vendor using a pre-configured template, logging the email in the 
chatter. 
○ The vendor responds (outside the system). The Purchasing Manager reviews 
the response, updates the prices in the RFQ, and changes its status to 
'Accepted'. 
○ The 'Convert to PO' button becomes active. The Manager clicks it, and the 
system creates a new PO, preserving all details from the RFQ. 
● UI/UX Considerations: 
○ A 'Send RFQ' button will be prominently displayed. 
○ An email preview pane will show the user what the vendor will receive. 
○ The 'Convert to PO' button will be greyed out and inactive until the RFQ status 
is 'Accepted'. 
● Business Logic & Technical Considerations: 
○ The system will prevent conversion to PO if the RFQ contains lines with a zero 
price, ensuring quotes are complete. 
○ A clear link will be maintained between the original RFQ and the resulting PO 
for audit purposes. 
● Acceptance Criteria: 
○ Given a Purchaser creates and saves an RFQ. 
○ When they click 'Send RFQ', then an email with a PDF attachment is sent to the 
vendor and a record of this email appears in the RFQ's chatter. 
○ When a Purchasing Manager changes the RFQ status to 'Accepted' and clicks 
'Convert to PO', then a new PO is created with its state as 'Purchase Order' and 
all line items and prices match the accepted RFQ. 
● Roles & Permissions: 
○ Purchaser (purchase.group_user): Can create and send RFQs. 
○ Purchasing Manager (purchase.group_purchase_manager): Can accept 
RFQs and convert them to POs. 
FR-PROD1: Centralized Product Attribute & Variant Configuration 
● Source(s): Vetlane ERP Comprehensive PRD (1).docx; Product Requirements 
Document (PRD)current.docx. 
● User Story: "As a Product Manager, I need to define product attributes like Size, 
Colour, and Design once, assign them to a product template, and have the system 
automatically generate all possible product variants, so that these variants and their 
specific prices and stock levels are accurately reflected across both the POS and the 
e-commerce website." 
● User Journey / Workflow: 
○ A Product Manager navigates to Inventory -> Configuration -> Product 
Attributes. They define attributes (e.g., 'Colour') and their values (e.g., 'Red', 
'Blue', 'Green'). 
○ They edit a 'Product Template' (e.g., 'Dog Collar'). 
○ They assign the 'Colour' and 'Size' attributes to this template. 
○ The system automatically creates 'Product Variants' for every combination (e.g., 
'Dog Collar - Red, Small', 'Dog Collar - Blue, Large'). 
○ The sales price for each specific variant can be set via a pricelist or overridden 
via the PO line sales price update (FR-PUR3). 
○ When a customer on the website or a staff member on the POS selects "Red" 
and "Large", the system shows the specific price, SKU, and stock level for that 
unique variant. 
● UI/UX Considerations: 
○ Website: The product page will display dropdown selectors for each attribute 
(e.g., Colour, Size). When a selection is made, the product image, price, SKU, 
and stock status on the page will dynamically update to reflect the chosen 
variant. 
○ POS/Sales: When adding a product with variants to a cart, a modal or selection 
step will appear, prompting the user to choose the specific attribute values 
before the item is added to the order. 
● Business Logic & Technical Considerations: 
○ This leverages Odoo's standard product.attribute and 
product.template functionality. 
○ The front-end (website and POS) will need JavaScript handlers to listen for 
changes on the attribute selectors and fire an AJAX call to fetch the 
corresponding variant's data. 
● Acceptance Criteria: 
○ Given a product template 'Shirt' is assigned the attributes 'Colour' (Red, Blue) 
and 'Size' (S, M). 
○ When the template is saved, then four unique product variants are created in 
the system (Red-S, Red-M, Blue-S, Blue-M). 
○ When a user on the website selects 'Red' and 'M' from the dropdowns, then 
the price and stock level shown on the page dynamically update to match the 
specific data for the 'Shirt - Red, M' variant. 
● Roles & Permissions: 
○ Product Manager (product.group_product_manager): Can define 
attributes and assign them to products. 
○ Web Admin (website.group_website_publisher): Manages how the 
variants are displayed on the website. 
Part 4: Business Process Functional Requirements (Grooming, Delivery, 
CRM) 
Grooming 
This set of requirements aims to replace the manual Vetlane- Grooming Boooking 
Sheet.xlsx with a fully integrated, efficient, and error-proof system within Odoo. 
FR-GRT1: Grooming Service & Booking Data Model 
● Source(s): Vetlane Erp Comprehensive Prd (1).docx. 
● User Story: "As a System Administrator, I need to configure all our grooming services, 
their durations, and prices as distinct products within Odoo, so that the Grooming 
Coordinator can book them accurately and consistently." 
● User Journey / Workflow: 
○ This is a one-time administrative setup task. 
○ The Administrator creates new products in Odoo of the type "Service." 
○ Each product corresponds to a grooming service (e.g., "Full Groom - Large 
Dog," "Nail Clipping"). 
○ The product form will include fields for standard duration and price, based on 
the existing booking sheet. 
○ A new vetlane.groom.booking model will be created (Devintel PET Module) 
to store booking records. Each record will link to a customer, a pet, a service 
(product), a start time, and an end time. 
● UI/UX Considerations: 
○ The service product setup will use the standard Odoo product form. 
● Business Logic & Technical Considerations: 
○ A new model, vetlane.groom.booking, will be created. It will have 
many2one relationships to res.partner (for the owner), the pet record 
model, and product.product (for the service). 
● Acceptance Criteria: 
○ Given the system is configured: 
■ When an Administrator navigates to the Products list, then they can see 
and edit specific service products for grooming. 
■ When a developer inspects the Odoo models, then a 
vetlane.groom.booking model exists with the required fields for 
booking appointments. 
● Roles & Permissions: 
○ System Administrator / Product Manager: Configures the grooming service 
products. 
FR-GRT2: Grooming Availability Slot Configuration 
● Source(s): Vetlane ERP Comprehensive Prd (1).docx. 
● User Story: "As a Grooming Coordinator, I need the booking system to be 
pre-configured with our exact operating hours and automatically prevent bookings on 
days we are closed, so I don't have to remember the schedule and cannot make a 
mistake." 
● User Journey / Workflow: 
○ The System Administrator configures the grooming availability in the backend. 
○ They define the available time slots based on the business's operating hours. 
○ When the Grooming Coordinator accesses the booking calendar/schedule view, 
the defined slots are open for booking, and all other times (including the entire 
day on Monday) are blocked out and unavailable. 
● UI/UX Considerations: 
○ The booking calendar view will visually grey out or block unavailable times, 
making it impossible to click and create a booking in those slots. 
● Business Logic & Technical Considerations: 
○ The system will be configured with the specific availability: 
■ Tuesday–Saturday: 09:00 – 19:00. 
■ Sunday: 13:00 – 19:00. 
■ Monday: Fully disabled. 
○ This can be implemented using Odoo's resource calendar and appointment 
scheduling features. 
● Acceptance Criteria: 
○ Given the Grooming Coordinator is viewing the booking calendar: 
■ When they look at any given Monday, then the entire day is blocked and 
no appointments can be created. 
■ When they attempt to create a booking on a Tuesday at 08:00, then the 
action is blocked because it is outside operating hours. 
■ When they create a booking on a Sunday at 14:00, then the action is 
successful. 
● Roles & Permissions: 
○ System Administrator: Configures the availability schedule. 
○ Grooming Coordinator: Uses the schedule to make bookings. 
FR-GRT3: Pet & Owner Record Lookup from Booking UI 
● Source(s): Vetlane ERP Comprehensive Prd (1).docx 
● User Story: "As a Grooming Coordinator, when a client calls to book an appointment, I 
need to instantly search for and link their existing pet and owner record from the main 
database, so I don't have to re-enter information and can immediately see the pet's 
history." 
● User Journey / Workflow: 
○ The Grooming Coordinator opens the grooming booking calendar/form. 
○ In the "Customer" or "Pet" field, they begin typing the owner's name, phone 
number, or the pet's name. 
○ The system performs a live search against the master contact and pet 
databases (which are synchronized per FR-CRM3). 
○ A dropdown list of matching results appears. 
○ The coordinator selects the correct pet/owner pair, and all relevant details 
(owner name, contact info, pet name, breed) are automatically populated into 
the booking form. 
○ If no record is found, an option to "+ Create New" is available. 
● UI/UX Considerations: 
○ The lookup fields will feature fast, as-you-type autocomplete functionality to 
minimize search time. 
● Business Logic & Technical Considerations: 
○ This feature requires a real-time data lookup from the grooming booking 
interface into the core res.partner (owner) and pet data models. 
○ Given that pet data is synchronized between the ACS and DevIntelle modules, 
this lookup must query the unified master record to ensure consistency. This 
may involve an RPC (Remote Procedure Call) or direct model access depending 
on the final architecture. 
● Acceptance Criteria: 
○ Given a pet named "Fluffy" owned by "Jane Doe" exists in the system. 
○ When the Grooming Coordinator types "Flu" or "Jane" into the search field of 
the booking form, then "Fluffy - Jane Doe" must appear as a selectable option 
in the results list. 
○ When the coordinator selects this option, then the owner and pet fields in the 
booking form are automatically and correctly populated. 
● Roles & Permissions: 
○ Grooming Coordinator: Can search, link, and create new pet/owner records 
from the booking interface. 
FR-GRT4: Excel-Style Custom Grooming Report View 
● Source(s): Vetlane ERP Comprehensive Prd (1).docx; Product Requirements Document 
(PRD)current.docx 
● User Story: "As a Grooming Coordinator, I need a report view that looks and feels like 
our current Excel sheet, allowing me to see the daily schedule at a glance and make 
inline edits, so the transition to the new system is seamless and my workflow remains 
efficient." 
● User Journey / Workflow: 
○ The Grooming Coordinator navigates to the Grooming Report section. 
○ The system displays a list/grid view of the day's appointments. 
○ The columns in this view will mirror the columns from the existing Vetlane- 
Grooming Boooking Sheet.xlsx (e.g., Time, Pet Name, Owner Name, 
Service, Notes, Status). 
○ The coordinator can click directly into a cell in the grid to make a quick edit 
(e.g., change the status from 'Booked' to 'In Progress') without opening the full 
form view. 
○ A button is available to export the current view to a PDF or CSV file. 
● UI/UX Considerations: 
○ This requires a custom Odoo tree view with the editable="bottom" or 
editable="top" attribute to allow for inline creation and editing, mimicking a 
spreadsheet. 
● Business Logic & Technical Considerations: 
○ A custom ir.actions.act_window and ir.ui.view will be created to 
define this specific report view. 
○ The view will pull data directly from the vetlane.groom.booking model. 
● Acceptance Criteria: 
○ Given the user is on the Grooming Report view. 
○ When they see the list of the day's appointments, then the columns and their 
order match the legacy Excel sheet. 
○ When they click on the "Status" cell of an appointment, then they can change 
the status directly in the grid without a page reload. 
○ When they click the "Export" button, then a PDF/CSV file is generated that 
reflects the current view. 
● Roles & Permissions: 
○ Grooming Coordinator / Branch Manager: Can view and edit the grooming 
report. 
Delivery Tracking 
These requirements formalize the delivery process, moving it from the manual Vetlane 
Delivery Master Sheet.xlsx into an integrated, trackable system. 
FR-DEL1: Zone-Based Delivery Fee Structure 
● Source(s): Vetlane Erp Comprehensive Prd (1).docx. 
● User Story: "As an Administrator, I need to set up our complex, zone-based delivery 
fees in the system based on our master sheet, so that delivery charges can be 
calculated automatically and consistently for all customers." 
● User Journey / Workflow: 
○ This is a one-time administrative setup task. 
○ The Admin navigates to a new "Delivery Zones" configuration area. 
○ They create records for each delivery zone (e.g., 'Lagos Mainland 1', 'Abuja 
Wuse 2'). 
○ For each zone, they enter the corresponding fee. The model should also 
support more complex rules like a base fee + per-kilometer rates if needed. 
○ They also configure free pickup options for the Lagos and Abuja branches. 
● UI/UX Considerations: 
○ A new configuration menu for "Delivery Zones" will be created under the 
Inventory or Sales application. 
● Business Logic & Technical Considerations: 
○ A new model, vetlane.delivery.zone, will be created to store this fee 
structure. 
● Acceptance Criteria: 
○ Given the system is configured according to the master sheet: 
■ When an Admin views the Delivery Zones list, then they see an entry for 
"Abuja [Jabi, Wuse]" with a fee of ₦2,000. 
■ And they see an entry for "Pick Up ABUJA" with a fee of ₦0. 
● Roles & Permissions: 
○ System Administrator: Manages the delivery zone fee structure. 
FR-DEL2: Dynamic Delivery Fee Calculation (POS & Website) 
● Source(s): Vetlane ERP Comprehensive Prd (1).docx 
● User Story: "As a customer checking out online, I want the system to automatically 
calculate the correct delivery fee for my location after I enter my address, so I know 
the final price upfront without any surprises." 
● User Journey / Workflow: 
○ Website: A customer proceeds to the one-page checkout and enters their 
shipping address. Based on the entered city/area, the system looks up the 
corresponding zone in the vetlane.delivery.zone model (from FR-DEL1) 
and automatically adds the correct delivery fee to the order total. 
○ POS: A staff member creates a POS order and adds a delivery service. They 
select the customer's delivery zone from a dropdown. The system automatically 
adds the correct fee to the POS cart. 
● UI/UX Considerations: 
○ On the website checkout page, the delivery fee and order total will update 
dynamically via AJAX after the address is entered. 
○ In the POS, selecting the delivery zone will instantly add the fee as a line item. 
● Business Logic & Technical Considerations: 
○ This feature is the execution layer for the data model defined in FR-DEL1. 
○ Logic must be implemented in both the website checkout controller and the 
POS JavaScript to query the vetlane.delivery.zone model and retrieve 
the correct fee. 
○ The logic must also account for the free "Pick Up" options. 
● Acceptance Criteria: 
○ Given the delivery fee for the "Abuja Wuse" zone is set to ₦2,000. 
○ When a customer on the website enters an address in Wuse, Abuja, then the 
order total must automatically increase by ₦2,000. 
○ When a POS user selects the "Abuja Wuse" zone for a delivery, then a line item 
for "Delivery Fee" with a price of ₦2,000 is added to the cart. 
● Roles & Permissions: 
○ E-commerce Customer / POS Users: Experience the automatic calculation. 
○ Supervisor: Can override the calculated fee in the POS (per FR-POS12). 
FR-DEL3: Spreadsheet-Style Delivery Tracking Interface 
● Source(s): Vetlane ERP Comprehensive Prd (1).docx; Product Requirements Document 
(PRD)current.docx. 
● User Story: "As a dispatch manager, I need a single, centralized screen that looks and 
functions like a spreadsheet, where I can see all pending deliveries and instantly update 
their statuses, so I can manage our entire delivery operation efficiently without manual 
tracking." 
● User Journey / Workflow: 
○ The dispatch manager navigates to the "Delivery Tracking" page within the Sales 
or Inventory application. 
○ The system displays a grid view (a list) of all sales orders that are marked for 
delivery. 
○ The columns in this view will be configured to match the legacy Vetlane 
Delivery Master Sheet.xlsx for familiarity (e.g., Order #, Customer Name, 
Address, Phone, Order Status, Delivery Status). 
○ To update a delivery's status, the manager clicks directly on the "Delivery Status" 
cell for that order. 
○ A dropdown menu appears with the available statuses ('Pending', 'Out for 
Delivery', 'Delivered', 'Failed', 'Rescheduled'). 
○ The manager selects the new status, and the change is saved instantly without a 
page reload. 
○ A button to "Export to CSV/Excel" is available to download the current view for 
reporting. 
● UI/UX Considerations & Wireframe Description: 
○ The interface will be a custom Odoo tree/list view designed for high information 
density and quick interaction. 
○ The core feature is inline editing. The 'Delivery Status' field will be a clickable 
dropdown directly within the list. 
○ The view will have built-in filters to easily see all deliveries for a specific day, 
status, or branch. 
● Business Logic & Technical Considerations: 
○ A custom ir.ui.view and ir.actions.act_window will be created to 
define this specific tracking page. 
○ The view will query sale.order or stock.picking records that have a 
delivery method selected. 
○ A new 'Delivery Status' Selection field will be added to the sale.order 
model to track this state independently of the main order status. 
○ Inline editing will be enabled on this field in the tree view. 
● Acceptance Criteria: 
○ Given a user is on the Delivery Tracking page. 
■ When they view the list of deliveries, then the columns must mirror the 
key columns of the legacy Excel sheet. 
■ When they click the status cell for an order that is currently 'Pending', 
then a dropdown appears with options like 'Out for Delivery'. 
■ When they select 'Out for Delivery' from the dropdown, then the status for 
that order immediately updates in the list without a page reload. 
■ When they click the "Export" button, then a CSV file containing the data 
from the current view is downloaded. 
● Roles & Permissions: 
○ Delivery Agent / Dispatch Manager / Supervisor: Can view the tracking 
interface and update delivery statuses. 
CRM & Client Data 
These requirements focus on creating a single, clean, and reliable source of truth for all 
customer and pet data. 
FR-CRM1: Contact Deduplication & Merge Wizard 
● Source(s): Vetlane Erp Comprehensive Prd (1).docx; Product Requirements Document 
(PRD)current.docx. 
● User Story: "As a front-desk employee, I need the system to proactively prevent me 
from creating duplicate customer records and provide a tool to merge existing 
duplicates, so that our customer database remains clean and we have a single, 
accurate view of every client." 
● User Journey / Workflow: 
○ Prevention: When a user creates a new contact (in the POS or backend) and 
enters a phone number or email that already exists, the system will immediately 
f
 lag it and prompt the user to use the existing contact instead. 
○ Merging: An authorized user can select two or more duplicate contact records 
from the contacts list. They then launch a "Merge Wizard." The wizard displays 
the data from all selected records side-by-side and allows the user to choose 
the master record and select which fields to keep from each duplicate. Upon 
confirmation, the duplicates are archived, and all their associated records 
(pets, sales, invoices) are reassigned to the one master contact. 
○ Differentiation: For cases where two different clients genuinely have the same 
name, the merge wizard will allow the user to differentiate them by appending a 
unique suffix (e.g., 'John Smith (2)') to one of them. 
● UI/UX Considerations: 
○ The merge wizard will have a clear, multi-column layout to easily compare 
records before merging. 
● Business Logic & Technical Considerations: 
○ A unique constraint will be considered for the phone and email fields on the 
res.partner model, with custom handling to provide a user-friendly prompt 
instead of a generic database error. 
○ The merge script must be robust, ensuring that all related documents 
(sale.order, account.move, pet records, etc.) are correctly re-linked to the 
master res.partner record. 
● Acceptance Criteria: 
○ Given a contact exists with the phone number '***********': 
■ When a user tries to create a new contact with the same phone number, 
then the system prevents the creation and presents a link to the existing 
contact. 
○ Given two duplicate contact records are selected for merging: 
■ When the user completes the merge wizard, then one contact remains 
active, the other is archived, and all sales orders from the archived 
contact are now linked to the active one. 
● Roles & Permissions: 
○ All Users: Will be subject to the duplicate prevention check. 
○ Supervisor / Admin: Can access and use the merge wizard. 
CRM & Client Data (Continued) 
FR-CRM2: WhatsApp Contact Field & Communications Opt-In 
● Source(s): Vetlane ERP Comprehensive Prd (1).docx; Product 
Requirements Document (PRD)current.docx 
● User Story: "As a Marketing Manager, I need to collect customers' 
WhatsApp numbers and get their explicit consent for communications, so 
we can legally and effectively send them appointment reminders and 
promotions via WhatsApp." 
● User Journey / Workflow: 
○ A staff member is creating or editing a customer's contact record. 
○ The contact form includes two new fields: a dedicated "WhatsApp 
Number" field and a checkbox labeled "WhatsApp Communication 
Opt-In." 
○ The staff member fills in the number and checks the box if the 
customer gives consent. 
● UI/UX Considerations: 
○ The new fields will be added to the main contact form 
(res.partner) in a logical location, likely near the standard phone 
and email fields. 
● Business Logic & Technical Considerations: 
○ Two new fields will be added to the res.partner model: 
whatsapp_number (Char field) and whatsapp_opt_in (Boolean 
f
 ield). 
○ All automated communication features (like FR-MRK1) that use 
WhatsApp must be configured to only send messages to contacts 
where whatsapp_opt_in is True. 
● Acceptance Criteria: 
○ Given a user is viewing a customer's contact form in Odoo. 
○ When they look at the contact details, then they see a field for 
"WhatsApp Number" and a checkbox for "WhatsApp 
Communication Opt-In." 
● Roles & Permissions: 
○ Sales Rep / Cashier / Supervisor / Admin: Can view and edit 
these fields on a contact record. 
FR-CRM3: Bi-Directional Pet & Owner Record Sync (ACS <> DevIntelle) 
● Source(s): Vetlane ERP Comprehensive Prd (1).docx; Product 
Requirements Document (PRD)current.docx 
● User Story: "As a System Administrator, I need to ensure that when a 
new pet is registered in the clinic's ACS module, it automatically appears 
in the DevIntelle grooming module, and vice versa, to create a single, 
unified view of every pet across all departments and eliminate duplicate 
data entry." 
● User Journey / Workflow: 
○ Scenario A: A Vet Clinician registers a new pet, "Buddy," under 
owner "John Doe" in the ACS Hospital Module. 
○ The synchronization mechanism detects this new record. 
○ Within a short, defined time frame, the same pet record "Buddy," 
linked to "John Doe," is automatically created and becomes 
searchable in the DevIntelle Pet Care module used by the grooming 
department. 
○ Scenario B: The reverse happens when the Grooming Coordinator 
creates a new pet record in the DevIntelle module; it is 
automatically created in the ACS module. 
● UI/UX Considerations: 
○ This is a backend process. The only UI is the result: staff in either 
department see the same, up-to-date information. 
● Business Logic & Technical Considerations: 
○ This is a high-complexity, high-risk technical task. 
○ A robust synchronization mechanism must be built. This could be a 
scheduled job that runs every few minutes or a more advanced 
trigger/webhook-based system. 
○ Field Mapping: A definitive mapping document must be created to 
specify which field in the ACS pet model corresponds to which 
f
 ield in the DevIntelle pet model (e.g., acs.pet.name -> 
dev.pet.name). 
○ Conflict Resolution: A policy must be defined for what happens if 
the same record is updated in both systems simultaneously. The 
default policy will likely be "last update wins," but this needs to be 
confirmed. 
○ An external ID mapping table will be necessary to keep track of 
linked records (e.g., ACS pet ID 123 corresponds to DevIntelle pet 
ID 456). 
● Acceptance Criteria: 
○ Given a new pet is created in the ACS module. 
○ When a user searches for that pet in the DevIntelle module after 
the defined sync interval (e.g., 5 minutes), then the pet's record is 
found. 
○ Given a pet's owner's phone number is updated in the DevIntelle 
module. 
○ When a user views that same pet's owner in the ACS module after 
the sync interval, then the updated phone number is reflected. 
● Roles & Permissions: 
○ System Administrator / Developer: Designs, implements, and 
monitors the synchronization mechanism. 
Part 5: E-Commerce & Website Functional Requirements 
FR-WEB1: Branch-Aware Stock Display 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As an online shopper, I need to know which branch I am shopping from 
and see if a product is in stock at that specific branch before I add it to my cart, so I 
don't waste my time trying to order an unavailable item." 
● User Journey / Workflow: 
○ A customer lands on the website. A prominent header element shows the 
currently selected shopping branch (e.g., "You're currently shopping from: 
Lagos Warehouse"). 
○ The customer can click this element to switch their shopping branch (e.g., to 
Abuja). 
○ When viewing a product page or a product listing, the system displays the 
stock status ("In Stock" or "Out of Stock") specifically for the selected branch. 
● UI/UX Considerations & Wireframe Description: 
○ The header will feature a branch selector dropdown, similar to the one depicted 
in the source document screenshot. 
○ On product pages, a clear text indicator (e.g., "Availability (Lagos): In Stock") 
will be displayed near the "Add to Cart" button. 
● Business Logic & Technical Considerations: 
○ The user's selected branch will be stored in their session data. 
○ All product stock queries on the website front-end will be filtered by the 
stock.warehouse location corresponding to the session's selected branch. 
● Acceptance Criteria: 
○ Given a product has 10 units in the Lagos warehouse and 0 in the Abuja 
warehouse. 
○ When a user has "Lagos Warehouse" selected as their branch and views the 
product, then the status shows "In Stock." 
○ When the user switches their branch to "Abuja Warehouse" and views the 
same product, then the status immediately updates to "Out of Stock." 
● Roles & Permissions: 
○ E-commerce Customer: Interacts with this feature. 
○ Web Admin: Manages the display and configuration. 
FR-WEB2: Product Variant Selection on Product Page 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As an online shopper looking at a dog collar, I need to be able to easily 
select the specific size and color I want from dropdown menus, and see the price and 
picture update immediately to reflect my choice, so I can be confident I'm ordering the 
correct item." 
● User Journey / Workflow: 
○ A customer navigates to a product that has variants (e.g., a shirt with different 
sizes and colors). 
○ The product page displays dropdown selectors for each attribute ("Size", 
"Colour"). 
○ When the customer selects a size and a color, the product's main image, SKU, 
price, and stock status dynamically update on the page without a full page 
reload. 
● UI/UX Considerations: 
○ Dropdown menus provide a clear and standard way to select options. 
○ The dynamic update of the price and image provides immediate visual 
feedback to the user, confirming their selection. 
● Business Logic & Technical Considerations: 
○ This is the front-end implementation of FR-PROD1. 
○ JavaScript on the product page will listen for onchange events on the attribute 
selectors. 
○ An AJAX call will be made to the Odoo backend to fetch the specific data 
(price, image, stock_quantity) for the selected product.variant. 
● Acceptance Criteria: 
○ Given a product 'Dog Collar' has two sizes, Small (₦1000) and Large (₦1500). 
○ When the user first loads the page, then the price shows ₦1000. 
○ When the user selects "Large" from the size dropdown, then the price 
displayed on the page must instantly update to ₦1500. 
● Roles & Permissions: 
○ E-commerce Customer: Interacts with this feature. 
FR-WEB3: YouTube Video Embedding on Product Page 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a Product Manager, I want to be able to add a YouTube video link to a 
product's page, so we can show product demonstrations and provide richer content to 
our customers." 
● User Journey / Workflow: 
○ An administrator edits a product in the Odoo backend. 
○ They paste a YouTube video URL into a dedicated "YouTube Link" field. 
○ On the website's product page, this video is automatically embedded and 
displayed responsively, typically beneath the product images. 
● UI/UX Considerations: 
○ A new field for the YouTube URL will be added to the product template form in 
the backend. 
○ The front-end will render this video in an embedded player that adjusts to the 
screen size. 
● Business Logic & Technical Considerations: 
○ The system will need to parse the YouTube URL to extract the video ID for 
embedding. 
● Acceptance Criteria: 
○ Given an administrator adds a valid YouTube link to a product's backend form. 
○ When a customer views that product's page on the website, then the YouTube 
video is embedded and playable on the page. 
● Roles & Permissions: 
○ Web Admin / Product Manager: Can add video links to products. 
Part 5: E-Commerce & Website Functional Requirements (Continued) 
FR-WEB4: Consolidated One-Page Checkout 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As an online shopper, I want to be able to enter my shipping address, 
select my shipping method, and choose my payment option on a single page, to make 
the checkout process as fast and frictionless as possible." 
● User Journey / Workflow: 
○ After reviewing their cart, the customer clicks "Proceed to Checkout." 
○ They are taken to a single, vertically-oriented page. 
○ This page contains three main sections: 
■ Section 1: Shipping Address form. 
■ Section 2: Shipping Method selection (displaying options and prices 
from FR-DEL1). 
■ Section 3: Payment Method selection (Flutterwave, Paystack, Bank 
Transfer). 
○ As the customer fills out each section, the page updates dynamically. There is a 
single "Place Order" button at the end of the page. 
● UI/UX Considerations & Wireframe Description: 
○ The layout will be a single, clean, multi-section form, avoiding the need for 
"Next" and "Back" buttons between steps. The screenshots in the source 
document showing the shipping address form and shipping methods will be 
used as a direct layout reference for these sections on the single page. The 
goal is to consolidate these distinct views into one flow. 
● Business Logic & Technical Considerations: 
○ This will require significant theme customization to override Odoo's default 
multi-step checkout process. 
○ JavaScript will be used to handle the display logic and update the order 
summary (e.g., adding the shipping cost) as the user makes selections. 
● Acceptance Criteria: 
○ Given a customer has items in their cart and clicks "Checkout." 
■ When the checkout page loads, then the fields for shipping address, 
the list of shipping methods, and payment options are all present on the 
same page. 
■ When the customer selects a shipping method, then the total order 
price updates instantly on the page without a full reload. 
● Roles & Permissions: 
○ E-commerce Customer: Interacts with this feature. 
FR-WEB5: Bank Transfer Payment with Remittance Voucher Upload 
● Source(s): Vetlane Erp Comprehensive Prd (1).docx; Product Requirements Document 
(PRD)current.docx. 
● User Story: "As an online shopper who prefers to pay by direct bank transfer, I need a 
clear process that shows me the account details, allows me to confirm I've paid, and 
provides a simple way to upload my payment proof so my order can be processed 
quickly." 
● User Journey / Workflow: 
○ At checkout, the customer selects the "Bank Transfer" payment option. 
○ The page displays Vetlane's bank account details (Bank Name, Account Name, 
Account Number). 
○ Below the details, a prominent button labeled "I have remitted money, fill in 
remittance voucher" is displayed. 
○ After making the transfer via their banking app, the customer clicks this button. 
○ They are taken to a new form titled "Bank Remittance Voucher". 
○ This form requires them to fill in: "Name of remitter," "Remittance bank 
account," "Transfer amount," "Reference Number," and an "Upload transfer 
voucher" file input field. 
○ After submitting this form, they are redirected to a "Thank You" page confirming 
receipt of their remittance details. 
● UI/UX Considerations & Wireframe Description: 
○ The flow will exactly match the screenshots provided in the source documents. 
○ The bank details page will be clean and easy to read, with copy-to-clipboard 
functionality for the account number. 
○ The voucher upload form will be simple, with clearly labeled text fields and a 
standard file-chooser button. 
● Business Logic & Technical Considerations: 
○ When an order is placed using this method, its status is initially 'On Hold' or 
'Pending Payment.' 
○ The submitted remittance voucher and form data will be attached to the 
corresponding sales order in the Odoo backend for the accounting team to 
review. 
○ Upon manual verification of the payment by the accountant, they will validate 
the order and trigger the fulfillment process. 
● Acceptance Criteria: 
○ Given a user selects "Bank Transfer" at checkout. 
■ When they click the "I have remitted money..." button, then they are 
taken to the remittance voucher form. 
■ When they submit the form with a file upload, then the uploaded file 
and form data are attached to their sales order in the Odoo backend. 
■ And the order status in the backend is 'On Hold' until an accountant 
manually confirms the payment. 
● Roles & Permissions: 
○ E-commerce Customer: Uses this payment workflow. 
○ Accountant: Reviews the submitted vouchers in the backend. 
FR-WEB6: Customer Profile with Order History, Tracking & Reviews 
● Source(s): Vetlane Erp Comprehensive Prd (1).docx; Product Requirements Document 
(PRD)current.docx. 
● User Story: "As a returning customer, I want to log into my account and see a list of my 
previous orders, check the tracking status of my current shipments, and be able to 
leave reviews on products I have purchased." 
● User Journey / Workflow: 
○ A customer logs into their account on the Vetlane website. 
○ They navigate to their profile or dashboard. 
○ The page has several sections/tabs: 
■ Order History: A list of all past and current orders, showing the order 
number, date, total amount, and status. Clicking an order shows its full 
details. 
■ Order Tracking: For orders that have been shipped, a tracker shows 
the current status (e.g., 'Processing', 'Shipped', 'Out for Delivery'). 
■ Reviews: On the details page of a past order, there is an option to rate 
(1-5 stars) and write a review for each product purchased. 
● UI/UX Considerations: 
○ The customer profile will be well-organized, likely using tabs or an accordion 
layout for a clean presentation of information. 
● Business Logic & Technical Considerations: 
○ The "Order History" section will query the user's associated sale.order 
records. 
○ The "Order Tracking" status will be linked to the status of the stock.picking 
record associated with the sale. 
○ The review functionality will link to Odoo's built-in rating.rating model. 
● Acceptance Criteria: 
○ Given a logged-in customer who has placed an order. 
■ When they navigate to their profile, then they can see that order listed 
in their history. 
■ When the backend status of the order's delivery is updated to 'Shipped', 
then the order tracking status on their profile page reflects this change. 
■ When they view a product from a past order, then a form is available for 
them to submit a 5-star rating and a text review. 
● Roles & Permissions: 
○ E-commerce Customer: Can view their own profile and leave reviews. 
○ Web Admin: Can manage and moderate submitted reviews. 
Part 5: E-Commerce & Website Functional Requirements (Conclusion) 
FR-WEB7: Themed Page Layouts (Home, Shop, New Arrivals, Clearance) 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a marketing manager, I need distinct, well-designed page layouts for 
our Home Page, main Shop page, a 'New Arrivals' section, and a 'Clearance' section, 
so we can strategically feature different products and create a professional, 
easy-to-navigate shopping experience." 
● User Journey / Workflow: 
○ A customer navigates the website using the main menu. 
○ Clicking "Home" takes them to the main landing page with featured banners 
and product carousels. 
○ Clicking "Shop" takes them to the main product category listing. 
○ Dedicated menu links for "New Arrivals" and "Clearance" take them to specially 
curated pages showing only products tagged with these attributes. 
● UI/UX Considerations & Wireframe Description: 
○ The website's theme will be customized to include templates for these specific 
pages. 
○ Home Page: Will feature promotional banners, featured product sections, and 
links to major categories. 
○ New Arrivals Page: A product grid showing the most recently added products. 
○ Clearance Page: A product grid showing products that are on sale or marked 
for clearance. 
● Business Logic & Technical Considerations: 
○ "New Arrivals" can be driven by a product's creation date. 
○ "Clearance" will require a boolean field or tag on the product template (e.g., 
is_clearance_item) that an admin can set. The Clearance page will then 
display a filtered list based on this tag. 
● Acceptance Criteria: 
○ Given the website is live: 
■ When a user clicks the "New Arrivals" link in the navigation menu, then 
they are taken to a page listing only the newest products. 
■ When an administrator tags a product as a 'Clearance' item in the 
backend, then that product automatically appears on the Clearance 
page on the website. 
● Roles & Permissions: 
○ Web Admin: Manages the content and product tagging for these pages. 
○ E-commerce Customer: Navigates and shops using these pages. 
FR-WEB8: Branch-Aware WhatsApp Live Chat Integration 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a customer on the website, if I have a question, I want to use a chat 
button to directly contact the specific branch I'm interested in, so I can get relevant 
answers about local stock or services." 
● User Journey / Workflow: 
○ A customer is Browse the website. They click on a floating "Chat" icon. 
○ A small window pops up, presenting clear choices: "Chat with Lagos Branch" 
and "Chat with Abuja Branch". 
○ The customer clicks on their desired branch. 
○ This action opens a new tab to WhatsApp Web or launches the WhatsApp 
desktop/mobile app. 
○ The chat message is pre-filled with a greeting and the URL of the exact page 
the customer was viewing, for context. For example: "Hi Vetlane, I have a 
question about this page: https://vetlane.com/product/example-product". 
● UI/UX Considerations & Wireframe Description: 
○ A floating chat bubble icon will be present on all pages. 
○ The popup will be simple and clear, presenting the branch choices as buttons. 
An example of a similar pop-up chat widget is shown in the screenshot 
*************.jpg. 
● Business Logic & Technical Considerations: 
○ The widget will need to be configured with the specific WhatsApp phone 
numbers for the Lagos and Abuja branches. 
○ JavaScript will be used to get the window.location.href (the current page 
URL) and encode it into the pre-filled WhatsApp message link. 
● Acceptance Criteria: 
○ Given a user is on a product page and clicks the chat icon. 
■ When they select "Chat with Lagos Branch," then a WhatsApp chat 
opens with the Lagos number, and the message includes the URL of the 
product page they were on. 
● Roles & Permissions: 
○ E-commerce Customer: Interacts with the chat widget. 
FR-WEB9: In-Stock Product Prioritization on Listings 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a shopper, I want to see all the products that are available for 
purchase first when I browse a category, so I don't have to scroll past lots of 
unavailable items to find what I can actually buy." 
● User Journey / Workflow: 
○ A customer navigates to a product category page (e.g., "Dog Food") or the 
main "Shop" page. 
○ The system automatically sorts the product grid. 
○ All products that are "In Stock" for the user's selected branch are displayed 
f
 irst. 
○ All products that are "Out of Stock" are displayed after all the in-stock items, at 
the bottom of the list. 
● UI/UX Considerations: 
○ "Out of Stock" items will have a clear visual treatment (e.g., greyed out image, 
"Out of Stock" badge) to differentiate them from available products. 
● Business Logic & Technical Considerations: 
○ The default sorting option for Odoo's e-commerce product listings will be 
overridden. 
○ The new primary sorting key will be a computed stock availability field (e.g., 
is_in_stock), ordered descending, followed by the standard sorting options 
like name or price. 
● Acceptance Criteria: 
○ Given a category has 10 products, 7 of which are in stock and 3 are out of 
stock. 
■ When a user loads that category page, then the first 7 products shown 
must be the ones that are in stock. The 3 out-of-stock products must 
appear at the end of the list. 
● Roles & Permissions: 
○ E-commerce Customer: Experiences this sorting logic. 
FR-WEB10: Mandatory Field Indicator Style Customization 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a Web Admin, I want to make the mandatory field indicator (the 
asterisk) red on all website forms, to provide a stronger, more conventional visual cue 
to users about which fields they are required to fill out." 
● User Journey / Workflow: 
○ A customer views any form on the website, such as the checkout shipping 
address form. 
○ The asterisk character (*) next to the label of each required field (e.g., 
"Name*", "Email*") is displayed in red. 
● UI/UX Considerations & Wireframe Description: 
○ This is a minor but important UI consistency tweak. 
○ The screenshot *************.jpg in the source documents clearly shows 
the shipping address form where the asterisks are currently black. This 
requirement is to change them to red. 
● Business Logic & Technical Considerations: 
○ This will be implemented by adding a simple CSS rule to the website's custom 
stylesheet. The rule will target the label or class associated with required form 
f
 ields and set its color property. For example: label.required .ast: { 
color: red; }. 
● Acceptance Criteria: 
○ Given a user is viewing the checkout page. 
■ When they look at the shipping address form, then the asterisk next to 
the "Name" and "Street and Number" fields must be rendered in the 
color red. 
● Roles & Permissions: 
○ E-commerce Customer: Experiences this UI styling. 
FR-WEB11: Individual Product Page WhatsApp Button 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a customer viewing a specific product, I want a dedicated WhatsApp 
button on that page to ask a question about that specific item, with the message 
pre-filled for me, so I don't have to manually type out what I'm looking at." 
● User Journey / Workflow: 
○ A customer is on a product page (e.g., for "Jungle Kitten Dry food 1.5kg"). 
○ They see a "Request via WhatsApp" button near the "Add to Cart" button. 
○ They click it. This opens WhatsApp. 
○ The chat message is pre-filled with a template like: "I want to buy [Product 
Name] at [Product URL]". For example: "I want to buy Jungle Kitten Dry food 
1.5kg at https://vetlane.com/product/jungle-kitten-dry-food-1-5kg". 
● UI/UX Considerations: 
○ This is a separate button from the site-wide floating chat widget. It will be 
located within the product detail section of the page. 
● Business Logic & Technical Considerations: 
○ Similar to FR-WEB8, this will use JavaScript to get the product's title and the 
page's URL and encode them into the WhatsApp message link. 
● Acceptance Criteria: 
○ Given a user is on the product page for "Stockfish Apama Head". 
■ When they click the "Request via WhatsApp" button on that page, then 
a WhatsApp chat opens with a pre-filled message that contains both 
the text "Stockfish Apama Head" and the full URL to that product's 
page. 
● Roles & Permissions: 
○ E-commerce Customer: Interacts with this feature. 
Part 6: Backend & Administrative Functional Requirements 
Clinic 
FR-CLN1: Post-Consultation Editing of Vital Signs 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a Vet Clinician, I need the ability to correct a pet's vital signs in the 
system even after I've initially saved them, to ensure the medical record is 100% 
accurate in case of a typo or mistaken entry." 
● User Journey / Workflow: 
○ A clinician completes a consultation and saves the pet's vital signs 
(temperature, weight, etc.). 
○ They later realize a value was entered incorrectly. 
○ They reopen the completed consultation record. 
○ The fields for vital signs are editable, allowing them to make the correction. 
○ They save the record, and the change is logged in the audit trail. 
● UI/UX Considerations: 
○ The fields for vital signs on a completed consultation form, which might 
normally be read-only, will be made editable for users in the clinician role. 
● Business Logic & Technical Considerations: 
○ The readonly attribute on the vital signs fields will be conditionally removed 
based on the user's group. 
○ To ensure accountability, every edit to these fields on a completed record must 
be logged in the chatter (mail.thread) with the old value, the new value, the 
user who made the change, and the timestamp. 
● Acceptance Criteria: 
○ Given a clinician has completed and saved a consultation with a pet's weight 
recorded as 5.2 kg. 
○ When they reopen that same consultation record, then the weight field is 
editable. 
○ When they change the weight to 5.0 kg and save, then the new value is stored, 
and a log appears in the record's chatter stating "Weight changed from 5.2 to 
5.0 by [Clinician Name]". 
● Roles & Permissions: 
○ Vet Clinician: Can edit vital signs on records they created or have access to. 
FR-CLN2: Post-Consultation Editing of Appointment Records 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a Vet Clinician, I need to be able to edit various parts of a patient's 
appointment record after the consultation is finished, such as general information or 
clinical advice, to add more details or make corrections for the official record." 
● User Journey / Workflow: 
○ A clinician re-opens a completed appointment/consultation record. 
○ They can modify fields within the 'general information', 'clinical assessment', 
'DD and Advice', and 'Disease history' sections. 
○ They save the record, and the changes are preserved. 
● UI/UX Considerations: 
○ Similar to FR-CLN1, fields in the specified sections will remain editable for 
authorized users even after the record is in a 'done' state. 
● Business Logic & Technical Considerations: 
○ The readonly state logic will be applied to the specific fields mentioned. 
○ All edits post-completion will be logged in the chatter for auditing purposes. 
● Acceptance Criteria: 
○ Given a completed appointment record. 
○ When a clinician opens the record, then they can modify the text within the 
"Advice" field. 
○ When they save the change, then the new text is saved and the modification is 
logged. 
● Roles & Permissions: 
○ Vet Clinician: Can edit appointment records. 
Laboratory & X-Ray 
FR-LAB1: Lab Test & X-Ray Scan Data Models & POS Integration 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a Branch Manager, I need to treat our Laboratory and X-Ray units as 
separate revenue centers with their own sellable services, so we can accurately track 
their financial performance and handle services for both internal and external clients." 
● User Journey / Workflow: 
○ Service Setup: Lab tests (e.g., 'Blood Panel') and X-ray scans (e.g., 'Chest 
X-Ray') are created as "Service" products in Odoo. 
○ POS Setup: Two new Point of Sale interfaces are configured: "Laboratory POS" 
and "X-Ray POS". 
○ Sale: When a client (either internal or from another clinic) requests a test, the 
staff uses the appropriate POS to sell the service product, generating revenue 
against that business unit. 
○ Data Entry: A new vetlane.lab.test or vetlane.xray.scan record is 
created, linked to the sale. Staff can then fill in the details and results of the 
test/scan in this record. 
● UI/UX Considerations: 
○ The data entry forms for lab tests and X-ray results should be structured and 
clear, with fields for all relevant details. 
● Business Logic & Technical Considerations: 
○ New models vetlane.lab.test and vetlane.xray.scan will be created. 
○ Two new pos.config records will be created to support the separate POS 
interfaces. This allows for distinct sales reports per unit. 
● Acceptance Criteria: 
○ Given the system is configured. 
○ When a staff member opens the POS list, then they see options for 
"Laboratory POS" and "X-Ray POS" in addition to the main petshop POS. 
○ When a sale is made through the "Laboratory POS," then the revenue from 
that sale is attributed to the Laboratory business unit in accounting reports. 
● Roles & Permissions: 
○ Lab Technician / X-Ray Technician: Uses the respective POS and fills in 
test/scan result forms. 
○ Accountant: Views sales reports generated from these POS interfaces. 
FR-LAB2: Automated PDF Report Generation & Storage 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a Lab Technician, once I finalize a lab test result, I need the system to 
automatically generate a professional PDF report that can be emailed to the client and 
is also saved permanently to the pet's profile, so we have a complete and accessible 
medical history." 
● User Journey / Workflow: 
○ A lab technician fills in all the result details on a vetlane.lab.test record. 
○ They click a "Finalize and Generate Report" button. 
○ The system uses a QWeb template to generate a professional-looking PDF 
document with the results. 
○ The generated PDF is automatically attached to the pet's master record in the 
system (CBMS). 
○ The system presents an option to email the report directly to the client. 
○ For X-rays, the user can also upload and attach the image files (e.g., JPEGs, 
DICOM files) to the vetlane.xray.scan record, which are then also stored 
on the pet's profile. 
● UI/UX Considerations: 
○ A professional, well-formatted PDF template will be designed for the lab and 
X-ray reports. 
○ A clear button to "Generate Report" will be on the data entry forms. 
● Business Logic & Technical Considerations: 
○ Odoo's built-in report generation engine (QWeb to PDF) will be used. 
○ The generated PDF and any uploaded images will be stored as attachments 
linked to the pet's res.partner or a dedicated pet model record. 
● Acceptance Criteria: 
○ Given a completed lab test record. 
○ When the user clicks "Finalize and Generate Report," then a PDF file is created 
and attached to the corresponding pet's profile. 
○ When a user uploads an X-ray image to an X-ray record, then that image file is 
also attached to the pet's profile. 
● Roles & Permissions: 
○ Lab Technician / X-Ray Technician: Can generate reports and upload 
images. 
○ Vet Clinician: Can view the stored reports and images on the pet's profile. 
Part 6: Backend & Administrative Functional Requirements (Continued) 
Accounting 
FR-ACC1: Anglo-Saxon Chart of Accounts & FIFO Costing 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As an Accountant, I need the ERP's accounting system to be configured 
for Anglo-Saxon accounting and use the FIFO costing method, to ensure that our Cost 
of Goods Sold is recognized at the time of sale and our inventory valuation accurately 
reflects the cost flow of our products." 
● User Journey / Workflow: 
○ This is a foundational, one-time configuration task performed during the initial 
setup of the Odoo environment. 
○ The System Administrator or implementation partner will set the Continental 
Accounting setting to "Anglo-Saxon Accounting." 
○ They will set the default costing method for product categories that are tracked 
by lot (i.e., most physical goods) to "First-In, First-Out (FIFO)." 
● UI/UX Considerations: 
○ This is a backend configuration with no direct end-user UI other than the 
settings panels in the Accounting and Inventory applications. 
● Business Logic & Technical Considerations: 
○ Anglo-Saxon Accounting: This ensures that when a product is delivered to a 
customer, the system automatically creates the corresponding journal entries 
to move the product's cost from the "Stock Valuation" asset account to the 
"Cost of Goods Sold" expense account. 
○ FIFO Costing Method: When a product is sold, the system will assign the cost 
of the oldest unit(s) of that product in inventory as the Cost of Goods Sold. This 
is critical for lot-tracked items with varying purchase costs. 
● Acceptance Criteria: 
○ Given the system is configured. 
■ When a product with a FIFO cost of ₦100 is sold and delivered, then 
the accounting journal entries must show an expense of ₦100 recorded 
in the Cost of Goods Sold account. 
■ When viewing the product category configuration, then the Costing 
Method must be set to 'First In First Out (FIFO)'. 
● Roles & Permissions: 
○ System Administrator / Implementation Partner: Performs the initial 
configuration. 
○ Accountant: Operates within the financial framework established by this 
configuration. 
FR-ACC2: Legacy Invoice Archival Strategy 
● Source(s): Vetlane Erp Comprehensive Prd (1).docx; Product Requirements Document 
(PRD)current.docx. 
● User Story: "As an Accountant, I need to migrate over 10,000 old and unreconciled 
invoices from our previous system, but they must be archived and excluded from 
active reports to prevent them from slowing down the new system and cluttering our 
day-to-day work." 
● User Journey / Workflow: 
○ During data migration, a script will import all legacy invoices (over 10,000 
records). 
○ The script will set a custom "Archived" flag on each of these imported records. 
○ These records will be made read-only. 
○ Standard accounting reports (e.g., P&L, Balance Sheet, Aged Receivables) will 
be configured to exclude any record marked as "Archived" by default. 
● UI/UX Considerations: 
○ Archived invoices should have a clear visual indicator (e.g., a greyed-out 
appearance or an "Archived" tag) when viewed in lists. 
● Business Logic & Technical Considerations: 
○ A custom boolean field is_legacy_archive will be added to the 
account.move model. 
○ Domain filters on all relevant financial reports and views will be modified to 
include [('is_legacy_archive', '=', False)]. This is critical to 
prevent slow loading speeds on the accounting module homepage. 
○ The import process will bring in these records but ensure they do not impact 
the opening financial balances of the new system. 
● Acceptance Criteria: 
○ Given the legacy data has been migrated. 
■ When an accountant runs a standard Aged Receivables report, then the 
legacy invoices must not appear in the report and must not affect its 
totals. 
■ When an accountant performs a search for an old invoice number, then 
they can find and view the record, but all fields are read-only. 
● Roles & Permissions: 
○ System Administrator: Manages the migration and archival process. 
○ Accountant: Can view archived records but cannot edit them. 
FR-ACC3: Standardized Invoice-to-Payment Workflow 
● Source(s): Vetlane Erp Comprehensive Prd (1).docx; Product Requirements Document 
(PRD)current.docx. 
● User Story: "As an Accountant, I need a clear and consistent workflow for managing 
invoices, from validating drafts and registering payments to fully reconciling them 
against our bank statements, including any associated bank charges." 
● User Journey / Workflow: 
○ An invoice is created in a 'Draft' state. 
○ The Accountant verifies the details and 'Validates' the invoice, moving it to the 
'Posted' state. 
○ When payment is received, the Accountant uses the 'Register Payment' 
function to apply the payment to the invoice. 
○ During bank reconciliation, the Accountant matches the payment against the 
bank statement line. If there is a difference due to bank charges, they use 
Odoo's reconciliation interface to attribute the difference to a 'Bank Charges' 
expense account. 
○ Once fully reconciled, the invoice is marked as 'Paid'. 
● UI/UX Considerations: 
○ The workflow will use Odoo's standard invoice and reconciliation screens. 
○ Reconciliation models for bank charges will be configured for easy selection 
during the reconciliation process. 
● Business Logic & Technical Considerations: 
○ The chart of accounts must include an expense account for "Bank Charges." 
○ Reconciliation models will be set up to suggest posting differences below a 
certain threshold to the Bank Charges account automatically. 
● Acceptance Criteria: 
○ Given a posted invoice for ₦10,000. 
○ When a payment of ₦9,950 is received from the customer and appears on the 
bank statement (with ₦50 being a bank charge). 
○ Then the Accountant can use the reconciliation screen to match the ₦9,950 
payment to the invoice and attribute the ₦50 difference to the "Bank Charges" 
account, successfully marking the ₦10,000 invoice as paid. 
● Roles & Permissions: 
○ Accountant: Manages the entire invoice-to-payment lifecycle. 
FR-ACC4: Bulk Legacy Invoice Reconciliation Wizard 
● Source(s): Vetlane Erp Comprehensive Prd (1).docx. 
● User Story: "As an Accountant tasked with cleaning up old records, I need a wizard 
that can help me reconcile a large number of the archived legacy invoices in bulk, 
based on matching rules, so I don't have to process them one by one." 
● User Journey / Workflow: 
○ The Accountant navigates to a new "Bulk Legacy Reconciliation" tool. 
○ The wizard presents options to filter the legacy invoices (e.g., by partner, date 
range). 
○ It allows the user to set matching rules (e.g., "match if partner and amount are 
identical," "match if reference numbers are the same"). 
○ The system presents a preview of the proposed reconciliations. 
○ The Accountant reviews and confirms, and the system processes the 
reconciliations in a batch job. 
● UI/UX Considerations: 
○ The wizard will be a simple multi-step form: Step 1 for filtering, Step 2 for rules, 
Step 3 for preview and confirmation. 
● Business Logic & Technical Considerations: 
○ This requires a custom wizard (TransientModel) that performs the filtering 
and matching logic. 
○ The script must be carefully designed to handle large volumes of records 
without timing out. It should likely process the final reconciliation as a 
scheduled background job. 
● Acceptance Criteria: 
○ Given there are 100 archived invoices for 'Customer X'. 
○ When the Accountant uses the wizard to match invoices for 'Customer X' 
against a legacy payment import, then the wizard presents a list of all potential 
matches for confirmation. 
○ When the Accountant confirms the batch, then the selected invoices are 
marked as reconciled in the system. 
● Roles & Permissions: 
○ Accountant / Admin: Can access and use the bulk reconciliation wizard. 
Part 6: Backend & Administrative Functional Requirements (Conclusion) 
Human Resources (Targeted for Phase 2) 
Note: The following HR modules are critical but are slated for implementation in Phase 2 of 
the project, after the core operational and commercial systems are stabilized. 
FR-HR1: Employee Attendance & Time Tracking 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As an HR Manager, I need all employees to log their attendance in Odoo, 
and I need a time tracker visible on the main dashboard, so we can accurately monitor 
work hours and ensure compliance with company policy." 
● User Journey / Workflow: 
○ An employee starts their workday and logs into Odoo. 
○ They use the "Attendances" module to check in, recording their start time. 
○ Throughout the day, they can potentially use a time tracker to log time against 
specific tasks or projects. 
○ At the end of the day, they check out, recording their end time. 
○ The HR manager can view attendance reports for all employees. 
● UI/UX Considerations: 
○ A time tracker widget or a link to the attendance module will be placed on the 
main Odoo dashboard (CBMS) for easy access. 
● Business Logic & Technical Considerations: 
○ This involves the installation and configuration of the standard Odoo 
"Attendances" module (hr_attendance). 
● Acceptance Criteria: 
○ Given the Attendances module is installed. 
■ When an employee logs in and checks in, then a new attendance record 
is created with their start time. 
■ When an HR Manager views the attendance reports, then they can see 
the check-in and check-out times for all employees for a given period. 
● Roles & Permissions: 
○ All Employees: Can check in and out for themselves. 
○ HR Manager: Can view reports for all employees and manage attendance 
records. 
FR-HR2: Daily Staff Report Submission via Notes/Chat 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a manager, I need a simple, centralized way for my staff to submit their 
daily work reports, so I can track progress and provide feedback without relying on 
external email or chat applications." 
● User Journey / Workflow: 
○ This is a procedural workflow enabled by existing Odoo tools. 
○ At the end of each day, an employee navigates to the Odoo "Notes" application 
or a designated internal chat channel. 
○ They create a new note or message with a standardized title (e.g., "Daily Report - [Employee Name] - [Date]"). 
○ They detail their completed tasks and any issues encountered. 
○ The manager can view these notes or channel messages to review the team's 
work. 
● UI/UX Considerations: 
○ Staff will be trained on the standardized format for submitting these reports. 
● Business Logic & Technical Considerations: 
○ This utilizes out-of-the-box Odoo features (note, mail) and does not require 
custom development, only process definition. 
● Acceptance Criteria: 
○ Given a clear procedure has been defined. 
■ When a manager checks the designated Notes or chat channel, then 
they can see all submitted daily reports from their team members in one 
place. 
● Roles & Permissions: 
○ All Employees: Can create notes and send chat messages. 
○ Managers: Can view notes and messages from their team. 
FR-HR3 & FR-HR4: Payroll & Appraisals Modules 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As an HR Manager, I need integrated modules for Payroll and Employee 
Appraisals to manage compensation and performance reviews directly within Odoo, 
ensuring all HR functions are centralized." 
● User Journey / Workflow: 
○ Payroll: The HR manager configures salary structures, rules for deductions 
(e.g., tax, pensions), and benefits. At the end of each pay period, they generate 
payslips for all employees from the system. 
○ Appraisals: The HR manager schedules performance appraisal campaigns. 
Employees and their managers receive notifications to complete 
self-assessments and manager reviews within the system. 
● UI/UX Considerations: 
○ These features will use the standard UI of the Odoo Payroll and Appraisals 
modules. 
● Business Logic & Technical Considerations: 
○ (Critical) Payroll: The Odoo Payroll module requires significant localization to 
comply with Nigerian labor laws, PAYE tax structures, and pension remittance 
requirements. This is a substantial configuration task. 
○ Appraisals (hr_appraisal): The standard module will be installed and 
configured. This also includes the requested modules for Recruitment and 
Employee Bonus. 
● Acceptance Criteria: 
○ Given the Payroll module is configured for Nigerian localization. 
■ When an HR manager generates a payslip for an employee, then the tax 
and pension deductions are calculated correctly according to Nigerian 
law. 
○ Given the Appraisals module is configured. 
■ When a manager completes an appraisal form for an employee, then 
the completed form is stored on the employee's record and is visible to 
both the employee and HR. 
● Roles & Permissions: 
○ HR Manager: Manages and administers Payroll and Appraisal functions. 
○ All Employees: Can view their own payslips and participate in their appraisals. 
Marketing & Communications   (PHASE 1 TASK) 
FR-MRK1: Kudi SMS Integration & Marketing Automation (PHASE 1 TASK) 
● Source(s): Product Requirements Document (PRD)current.docx. 
● User Story: "As a Marketing Manager, I need to connect our Odoo instance to the Kudi 
SMS gateway and set up marketing automations, so we can send bulk SMS campaigns 
and automated reminders for appointments and vaccinations and invoice debts, 
improving customer engagement and retention." 
● User Journey / Workflow: 
○ Integration: The system is configured with the API credentials for the Kudi SMS 
Nigeria gateway. 
○ Automation: The Marketing Manager creates an automated campaign. For 
example: "Grooming Reminder." 
■ Trigger: Appointment date is 24 hours away. 
■ Action: Send a pre-defined SMS template to the client's phone number 
via Kudi SMS. 
○ Campaigns: The manager can also create contact lists (e.g., "All Cat Owners") 
and send one-off bulk SMS marketing messages. 
● UI/UX Considerations: 
○ This will use the standard Odoo Marketing Automation interface, which 
provides a visual workflow builder. 
● Business Logic & Technical Considerations: 
○ A specific Odoo connector module for Kudi SMS will need to be developed or 
acquired. 
○ The system must correctly pull the customer's phone number from their 
contact record for sending the SMS. 
● Acceptance Criteria: 
○ Given a customer has a grooming appointment scheduled for tomorrow. 
■ When the automated campaign runs, then the customer receives an 
SMS reminder about their appointment via the Kudi SMS gateway. 
● Roles & Permissions: 
○ Marketing Manager: Creates and manages automation campaigns and SMS 
sending. 
○ System Administrator: Configures the initial gateway integration. 
Part 7: The Closing Framework 
7.1. Non-Functional Requirements (NFRs) 
These are the quality standards the system must adhere to. They are as critical as any 
functional feature. 
Category 
Requirement 
Metric / Target 
Source(s
 ) 
Page Load Speed: 
Performanc
 e 
Security 
Usability 
Standard pages (Home, 
Product, Category) must 
load quickly. 
Transaction Speed: Core 
transactions must feel 
instantaneous to the user. 
Data Processing: Backend 
jobs must not degrade 
user-facing performance. 
Data Encryption: All 
communication and 
sensitive data must be 
secured. 
Access Control: System 
access must be strictly 
limited by role. 
Auditability: All critical 
f
 inancial and 
data-modifying actions 
must be logged. 
Learnability: New staff 
must be able to adopt the 
system quickly. 
Error Handling: Errors 
must be clear and guide 
the user to a solution. 
2 seconds for 95th percentile 
users. 
POS sale completion       
1 second.    
E-commerce order placement       
2 seconds. 
Nightly expiry cron job to complete 
in       
15 minutes.     Report 
generation (e.g., EOD sales)       
seconds. 
30 
TLS 1.3 enforced on all endpoints.     
AES-256 encryption for sensitive 
customer data at rest. 
Adherence to Principle of Least 
Privilege. ACLs must prevent a 
Sales Rep from accessing any 
Supervisor-level functions. 
Comprehensive audit trails for PO 
edits, price changes, and returns 
must be maintained. 
A new user should be able to 
perform their core tasks (e.g., a 
cashier processing a sale) with       
15 minutes of training. 
Validation errors must specify 
which field is incorrect and why. 
System errors must provide a 
transaction ID for support. 
Scalability 
Concurrent Users: The 
system must handle peak 
business hours without 
degradation. 
Data Volume: The system 
must handle the projected 
growth in data. 
Localization Currency & Formatting: 
Must be configured for the 
Nigerian market. 
7.2. Data Migration Plan 
Support ≥50 concurrent POS and 
back-office users. 
Handle ≥100,000 SKUs and a 
customer database growing by 
20% annually without 
performance degradation. 
Default currency is Nigerian Naira 
(NGN). Dates use DD/MM/YYYY 
format. Primary language is 
English (NG). 
The transition from the Odoo 15 instance and manual files to the new Odoo 18 platform will be 
executed as follows: 
1. Customer & Pet Data: 
○ Action: Export all res.partner and associated pet records. Run a 
deduplication script to identify and merge contacts based on unique phone 
numbers and email addresses. 
○ Tool: Custom Python script, followed by Odoo's import/export tools. 
○ Outcome: A single, clean master list of all clients and their pets, ready for 
import. 
2. Product Data: 
○ Action: Export all products to CSV. Clean up and standardize the data 
according to the new product.template.xlsx format. Re-import into Odoo 
18. 
○ Tool: Spreadsheet software (for cleanup), Odoo import/export. 
○ Outcome: A clean and structured product catalog with all necessary attributes. 
3. Legacy Invoices (>10,000 Records): 
○ Action: Migrate all historical invoices from the Odoo 15 system. During the 
import, a custom script will set an is_legacy_archive flag to True. 
○ Tool: Direct database migration script or custom XML-RPC/OERPLib script. 
○ Outcome: All historical data is preserved for lookup but is read-only and 
excluded from active financial reports, preventing system slowdown. 
4. Opening Balances: 
○ Action: On the agreed cut-off date, export the trial balance and inventory 
valuation reports. These will be used to create the opening journal entries and 
set the initial stock levels in the new system. 
○ Tool: Odoo's journal entry import and inventory adjustment tools. 
○ Outcome: The new system starts with financially balanced and accurate 
opening numbers. 
7.3. Assumptions, Risks, and Constraints 
Type 
Assumption
 s 
Description 
1. API Availability: The project assumes the availability of stable, 
documented APIs for third-party services, including Kudi SMS, Paystack, 
and Flutterwave.      
2. Module Compatibility: Assumes that the purchased ACS Hospital 
Module and DevIntelle Pet Care Management modules have 
compatible data structures that will allow for the required bi-directional 
sync.      
3. Data Quality: Assumes that the source data from master sheets 
(Grooming, Delivery) is accurate and reflects the desired business logic. 
Risks 
1. Data Sync Complexity (High): The bi-directional sync between the 
ACS and DevIntelle pet modules is technically complex and could lead to 
data corruption or race conditions if not engineered carefully.  
Mitigation: Develop and test the sync in a dedicated staging test 
environment. Implement robust logging and a manual conflict resolution 
queue before moving to live database for the initial launch phase.      
2. User Resistance (Medium): Staff accustomed to old or manual 
workflows may resist the new, more structured processes.  
Mitigation: Conduct phased rollouts starting with a pilot branch. Provide 
comprehensive, role-specific training sessions and clear documentation 
(including screen recordings from FR-MSC2).      
3. Data Migration Errors (Medium): The large volume of legacy invoices 
and contacts presents a risk of errors during migration.  
Mitigation: Perform multiple test migrations on a staging server. Develop 
validation scripts to check data integrity post-migration. 
Constraints 1. System Version: The entire project is constrained to development and 
deployment on the Odoo 18 platform. 
7.4. Open Questions & Decisions Log 
This log tracks key issues and decisions, ensuring transparency and alignment. 
ID 
Q
00
 1 
Q
00
 2 
D
00
 1 
D
00
 2 
Type 
Questio
 n 
Questio
 n 
Decisio
 n 
Decisio
 n 
Description 
The initial notes mentioned an 
"ODOO AI MODULE". This was 
placed out-of-scope for Phase 
1. Do we need to build any 
foundational hooks for a future 
AI integration? 
FR-DEL2 requires zone-based 
fee calculation. The notes also 
mention calculating delivery 
based on weight/volume. 
Which logic takes precedence, 
or should it be a hybrid model? 
The system will update the 
product's standard sales price 
(list_price) directly from an 
editable Sale Price field on 
the Purchase Order form. 
The accounting method will be 
Anglo-Saxon with FIFO as the 
costing method. 
Status 
Date 
Logge
 d 
Open 12-Jun
2025 
Open 12-Jun
2025 
Decide
 d 
Decide
 d 
12-Jun
2025 
12-Jun
2025 
Resolution / Next 
Step 
Decision required 
from project 
stakeholders on 
long-term AI strategy. 
Requires clarification 
on the business logic 
for shipping cost 
calculation. 
This provides the 
simplest workflow for 
the purchasing team. 
Standard pricelist 
rules will still override 
this base price. 
This was a specific 
requirement for 
accurate Cost of 
Goods Sold tracking. 
7.5. Appendices 
● Appendix A. Branch & Module Inventory: 
○ Refers to the provided ir.module.module.xlsx.xlsx - Sheet 1.csv file 
for a complete list of currently active and newly purchased Odoo modules. 
● Appendix B. Grooming & Delivery Templates: 
○ Refers to the provided Vetlane- Grooming Boooking Sheet.xlsx - 
Sheet1.csv and Vetlane Delivery Master Sheet.xlsx files, which 
define the master data for these services. 
● Appendix C. Glossary: 
○ ACS: Almighty Consulting Pet/Hospital Management Module. 
○ DevIntelle: DevIntelle Pet Care Management Module. 
○ CBMS: Client's Business Management System (referring to the Odoo ERP 
instance). 
○ FIFO: First-In, First-Out inventory costing method. 
● Appendix D. Wireframes & Mockups: 
○ This appendix will house all visual designs. The following mockups provided in 
the source files are the primary reference for their respective features: 
■ FR-WEB8: WhatsApp Chat Widget - *************.jpg 
■ FR-WEB5: Bank Transfer Flow - *************.jpg, 
*************.jpg, *************.jpg, *************.jpg, 
*************.jpg 
■ FR-WEB1 & FR-WEB10: E-commerce UI and Address Form - 
*************.jpg 